<?php
/**
 * api/admin/ad_stats_api.php
 * API для получения статистики рекламных показов
 */

// Проверяем аутентификацию
session_start();
require_once __DIR__ . '/auth.php';

if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Не авторизован']);
    exit;
}

header('Content-Type: application/json');

/**
 * Парсинг лог-файла рекламных запросов
 */
function parseAdRequestsLog($dateFrom = null, $dateTo = null, $adType = null, $status = null, $country = null) {
    $logFile = __DIR__ . '/../ad_requests.log';
    
    if (!file_exists($logFile)) {
        return [];
    }
    
    $requests = [];
    $handle = fopen($logFile, 'r');
    
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            $line = trim($line);
            if (empty($line)) continue;
            
            $data = json_decode($line, true);
            if (!$data) continue;
            
            // Применяем фильтры
            if ($dateFrom && $data['timestamp'] < strtotime($dateFrom)) continue;
            if ($dateTo && $data['timestamp'] > strtotime($dateTo . ' 23:59:59')) continue;
            if ($adType && $data['ad_type'] !== $adType) continue;
            if ($status && $data['status'] !== $status) continue;
            if ($country && $data['country'] !== $country) continue;
            
            $requests[] = $data;
        }
        fclose($handle);
    }
    
    return $requests;
}

/**
 * Получение статистики по рекламным запросам
 */
function getAdStatistics($requests) {
    $stats = [
        'total_requests' => 0,
        'successful_shows' => 0,
        'empty_requests' => 0,
        'rate_limit_errors' => 0, // Счетчик для 429 ошибок
        'limit_exceeded' => 0,
        'blocked_users' => 0,
        'button_clicks' => 0,
        'ad_requests' => 0,
        'no_ads_available' => 0,
        'by_ad_type' => [],
        'by_country' => [],
        'by_date' => [],
        'by_hour' => [],
        'success_rate' => 0,
        'top_countries' => [],
        'top_users' => []
    ];
    
    $userStats = [];
    
    foreach ($requests as $request) {
        // ИСПРАВЛЕНИЕ: Считаем только клики по кнопкам как "запросы"
        if ($request['status'] === 'button_click') {
            $stats['total_requests']++;
        }

        // Статистика по статусам
        switch ($request['status']) {
            case 'success':
                $stats['successful_shows']++;
                break;
            case 'empty':
                $stats['empty_requests']++;
                break;
            case 'ad_error':
                // Обрабатываем только 429 ошибки (ограничения сервера)
                $reason = $request['reason'] ?? '';

                // 429 ошибки - отдельная категория (ограничения сервера)
                if (strpos($reason, '429 Too Many Requests') !== false || strpos($reason, '429') !== false) {
                    $stats['rate_limit_errors']++;
                }
                // Остальные ошибки игнорируем (не показываем в админке)
                break;
            case 'error':
                // Общие ошибки игнорируем (не показываем в админке)
                break;
            case 'limit_exceeded':
            case 'limit_reached':
            case 'general_limit_exceeded':
                $stats['limit_exceeded']++;
                break;
            case 'blocked_user':
                $stats['blocked_users']++;
                break;
            case 'button_click':
                $stats['button_clicks']++;
                break;
            case 'ad_request':
                $stats['ad_requests']++;
                break;
            case 'no_ads_available':
                $stats['no_ads_available']++;
                break;
            case 'ad_already_showing':
                if (!isset($stats['ad_already_showing'])) $stats['ad_already_showing'] = 0;
                $stats['ad_already_showing']++;
                break;
            case 'cooldown_active':
                if (!isset($stats['cooldown_active'])) $stats['cooldown_active'] = 0;
                $stats['cooldown_active']++;
                break;
            case 'ads_unavailable':
                if (!isset($stats['ads_unavailable'])) $stats['ads_unavailable'] = 0;
                $stats['ads_unavailable']++;
                break;
        }
        
        // Статистика по типам рекламы
        $adType = $request['ad_type'];
        if (!isset($stats['by_ad_type'][$adType])) {
            $stats['by_ad_type'][$adType] = [
                'total' => 0,
                'success' => 0,
                'empty' => 0,
                'rate_limit_errors' => 0, // Поле для 429 ошибок
                'button_clicks' => 0,
                'ad_requests' => 0,
                'no_ads' => 0
            ];
        }

        // ИСПРАВЛЕНИЕ: Считаем только клики по кнопкам в 'total'
        if ($request['status'] === 'button_click') {
            $stats['by_ad_type'][$adType]['total']++;
        }

        switch ($request['status']) {
            case 'success':
                $stats['by_ad_type'][$adType]['success']++;
                break;
            case 'empty':
                $stats['by_ad_type'][$adType]['empty']++;
                break;
            case 'ad_error':
                // Обрабатываем только 429 ошибки (ограничения сервера)
                $reason = $request['reason'] ?? '';

                // 429 ошибки - отдельная категория (ограничения сервера)
                if (strpos($reason, '429 Too Many Requests') !== false || strpos($reason, '429') !== false) {
                    $stats['by_ad_type'][$adType]['rate_limit_errors']++;
                }
                // Остальные ошибки игнорируем (не показываем в админке)
                break;
            case 'error':
                // Общие ошибки игнорируем (не показываем в админке)
                break;
            case 'button_click':
                $stats['by_ad_type'][$adType]['button_clicks']++;
                break;
            case 'ad_request':
                $stats['by_ad_type'][$adType]['ad_requests']++;
                break;
            case 'no_ads_available':
                $stats['by_ad_type'][$adType]['no_ads']++;
                break;
        }
        
        // Статистика по странам (только для кликов по кнопкам)
        if ($request['status'] === 'button_click') {
            $country = $request['country'];
            if (!isset($stats['by_country'][$country])) {
                $stats['by_country'][$country] = 0;
            }
            $stats['by_country'][$country]++;
        }
        
        // Статистика по дням (только для кликов по кнопкам)
        if ($request['status'] === 'button_click') {
            $date = date('Y-m-d', $request['timestamp']);
            if (!isset($stats['by_date'][$date])) {
                $stats['by_date'][$date] = [
                    'total' => 0,
                    'success' => 0
                ];
            }
            $stats['by_date'][$date]['total']++;
        }
        if ($request['status'] === 'success') {
            $date = date('Y-m-d', $request['timestamp']);
            if (!isset($stats['by_date'][$date])) {
                $stats['by_date'][$date] = [
                    'total' => 0,
                    'success' => 0
                ];
            }
            $stats['by_date'][$date]['success']++;
        }

        // Статистика по часам (только для кликов по кнопкам)
        if ($request['status'] === 'button_click') {
            $hour = date('H', $request['timestamp']);
            if (!isset($stats['by_hour'][$hour])) {
                $stats['by_hour'][$hour] = 0;
            }
            $stats['by_hour'][$hour]++;
        }
        
        // Статистика по пользователям
        $userId = $request['user_id'];
        if ($userId > 0) {
            if (!isset($userStats[$userId])) {
                $userStats[$userId] = [
                    'total' => 0,
                    'success' => 0
                ];
            }
            $userStats[$userId]['total']++;
            if ($request['status'] === 'success') {
                $userStats[$userId]['success']++;
            }
        }
    }
    
    // Вычисляем процент успешности (от кликов по кнопкам)
    if ($stats['button_clicks'] > 0) {
        $stats['success_rate'] = round(($stats['successful_shows'] / $stats['button_clicks']) * 100, 2);
    }
    
    // Топ стран
    arsort($stats['by_country']);
    $stats['top_countries'] = array_slice($stats['by_country'], 0, 10, true);
    
    // Топ пользователей
    uasort($userStats, function($a, $b) {
        return $b['success'] - $a['success'];
    });
    $stats['top_users'] = array_slice($userStats, 0, 10, true);
    
    // Сортируем данные по дням
    ksort($stats['by_date']);
    
    return $stats;
}

// Получаем параметры фильтрации
$dateFrom = $_GET['date_from'] ?? null;
$dateTo = $_GET['date_to'] ?? null;
$adType = $_GET['ad_type'] ?? null;
$status = $_GET['status'] ?? null;
$country = $_GET['country'] ?? null;
$export = $_GET['export'] ?? null;

try {
    // Парсим лог-файл
    $requests = parseAdRequestsLog($dateFrom, $dateTo, $adType, $status, $country);

    // Получаем статистику
    $statistics = getAdStatistics($requests);

    // Если запрошен экспорт
    if ($export) {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="ad_statistics_' . date('Y-m-d') . '.json"');

        echo json_encode([
            'export_date' => date('Y-m-d H:i:s'),
            'filters' => [
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'ad_type' => $adType,
                'status' => $status,
                'country' => $country
            ],
            'statistics' => $statistics,
            'raw_data' => $requests
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }

    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'data' => $statistics,
        'filters' => [
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'ad_type' => $adType,
            'status' => $status,
            'country' => $country
        ],
        'total_records' => count($requests)
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения статистики: ' . $e->getMessage()
    ]);
}
?>
