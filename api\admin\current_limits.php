<?php
/**
 * api/admin/current_limits.php
 * API для получения текущих лимитов системы безопасности
 */

// Проверка авторизации администратора
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Требуется авторизация администратора']);
    exit;
}

// Подключаем функции безопасности
require_once __DIR__ . '/../security.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // Получаем текущие лимиты
    $limitsInfo = getCurrentLimits();
    
    // Добавляем дополнительную информацию
    $response = [
        'success' => true,
        'timestamp' => time(),
        'current_time_utc' => gmdate('Y-m-d H:i:s'),
        'current_time_local' => date('Y-m-d H:i:s'),
        'limits_info' => $limitsInfo,
        'status' => $limitsInfo['is_peak_time'] ? 'PEAK_TIME' : 'NORMAL_TIME',
        'multipliers' => [
            'peak_hourly_multiplier' => 2.0,
            'peak_daily_multiplier' => 1.5
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("current_limits.php ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Ошибка получения информации о лимитах',
        'success' => false
    ]);
}
?>
