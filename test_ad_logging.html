<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы логирования рекламы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест системы логирования рекламы</h1>
        
        <div class="test-section">
            <h3>Настройка тестовой среды</h3>
            <button class="test-button" onclick="setupTestEnvironment()">Настроить тестовую среду</button>
            <div id="setup-status"></div>
        </div>

        <div class="test-section">
            <h3>Тест кликов по кнопкам</h3>
            <button class="test-button" onclick="testButtonClick('native_banner')">Тест клика: Автопереход</button>
            <button class="test-button" onclick="testButtonClick('rewarded_video')">Тест клика: Видео</button>
            <button class="test-button" onclick="testButtonClick('interstitial')">Тест клика: Интерстициал</button>
        </div>

        <div class="test-section">
            <h3>Тест различных сценариев</h3>
            <button class="test-button" onclick="testScenario('limit_reached')">Тест: Лимит достигнут</button>
            <button class="test-button" onclick="testScenario('no_ads')">Тест: Нет рекламы</button>
            <button class="test-button" onclick="testScenario('ad_error')">Тест: Ошибка рекламы</button>
            <button class="test-button" onclick="testScenario('cooldown_active')">Тест: Активен кулдаун</button>
        </div>

        <div class="test-section">
            <h3>Проверка геолокации</h3>
            <button class="test-button" onclick="testGeolocation()">Тест определения страны</button>
            <div id="geo-result"></div>
        </div>

        <div class="test-section">
            <h3>Лог запросов</h3>
            <button class="test-button" onclick="clearLog()">Очистить лог</button>
            <button class="test-button" onclick="refreshLog()">Обновить лог</button>
            <div id="log-output" class="log-output"></div>
        </div>
    </div>

    <script>
        // Настройка тестовой среды
        function setupTestEnvironment() {
            // Эмулируем Telegram WebApp
            window.Telegram = {
                WebApp: {
                    initData: 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22Test%22%2C%22last_name%22%3A%22User%22%2C%22username%22%3A%22testuser%22%2C%22language_code%22%3A%22ru%22%7D&auth_date=1234567890&hash=test_hash'
                }
            };

            // Настройка API базового URL
            window.API_BASE_URL = './api';

            // Настройка session ID
            window.sessionStorage.setItem('session_id', 'test_session_' + Date.now());

            document.getElementById('setup-status').innerHTML = 
                '<div class="status success">✅ Тестовая среда настроена</div>';
        }

        // Тест клика по кнопке
        async function testButtonClick(adType) {
            try {
                const response = await fetch('./api/logAdClick.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        initData: window.Telegram?.WebApp?.initData || 'test_data',
                        adType: adType,
                        clickType: 'button_click',
                        timestamp: Date.now(),
                        sessionId: window.sessionStorage.getItem('session_id') || 'test_session'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    addToLog(`✅ Клик ${adType}: ${result.message}`);
                } else {
                    addToLog(`❌ Ошибка клика ${adType}: ${result.error}`);
                }
            } catch (error) {
                addToLog(`❌ Ошибка запроса: ${error.message}`);
            }
        }

        // Тест различных сценариев
        async function testScenario(scenario) {
            const scenarios = {
                'limit_reached': {
                    clickType: 'limit_reached',
                    reason: 'Daily limit of 20 ads reached'
                },
                'no_ads': {
                    clickType: 'no_ads_available',
                    reason: 'No ads available from ad network'
                },
                'ad_error': {
                    clickType: 'ad_error',
                    reason: 'Failed to load ad content'
                },
                'cooldown_active': {
                    clickType: 'cooldown_active',
                    reason: 'Cooldown period active, 15 seconds remaining'
                }
            };

            const config = scenarios[scenario];
            if (!config) return;

            try {
                const response = await fetch('./api/logAdClick.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        initData: window.Telegram?.WebApp?.initData || 'test_data',
                        adType: 'native_banner',
                        clickType: config.clickType,
                        reason: config.reason,
                        timestamp: Date.now(),
                        sessionId: window.sessionStorage.getItem('session_id') || 'test_session'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    addToLog(`✅ Сценарий ${scenario}: ${result.message}`);
                } else {
                    addToLog(`❌ Ошибка сценария ${scenario}: ${result.error}`);
                }
            } catch (error) {
                addToLog(`❌ Ошибка запроса: ${error.message}`);
            }
        }

        // Тест геолокации
        async function testGeolocation() {
            try {
                const response = await fetch('./api/recordAdView.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test_geo: true
                    })
                });

                const result = await response.text();
                document.getElementById('geo-result').innerHTML = 
                    `<div class="status info">Результат: ${result}</div>`;
            } catch (error) {
                document.getElementById('geo-result').innerHTML = 
                    `<div class="status error">Ошибка: ${error.message}</div>`;
            }
        }

        // Добавить в лог
        function addToLog(message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Очистить лог
        function clearLog() {
            document.getElementById('log-output').innerHTML = '';
        }

        // Обновить лог
        function refreshLog() {
            // В реальном приложении здесь был бы запрос к API для получения последних логов
            addToLog('📄 Лог обновлен');
        }

        // Автоматическая настройка при загрузке
        window.addEventListener('load', function() {
            setupTestEnvironment();
            addToLog('🚀 Тестовая страница загружена');
        });
    </script>
</body>
</html>
