<?php
/**
 * api/getWithdrawalHistory.php
 * API эндпоинт для получения истории выплат пользователя.
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in getWithdrawalHistory.php'); 
    echo json_encode(['error'=>'Ошибка сервера: CFG']); 
    exit; 
}
if (!(@require_once __DIR__ . '/validate_initdata.php')) {
    http_response_code(500);
    error_log('FATAL: validate_initdata.php not found in getWithdrawalHistory.php');
    echo json_encode(['error'=>'Ошибка сервера: VID']);
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in getWithdrawalHistory.php'); 
    echo json_encode(['error'=>'Ошибка сервера: DBM']); 
    exit; 
}
// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);
error_log("getWithdrawalHistory DEBUG: Получен запрос: " . json_encode($input));

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    error_log("getWithdrawalHistory ERROR: Нет initData в запросе");
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Нет данных']);
    exit;
}
$initData = $input['initData'];
error_log("getWithdrawalHistory INFO: Получен initData (длина: " . strlen($initData) . "): " . substr($initData, 0, 100) . "...");

// 2. Валидация initData
$validatedData = validateTelegramInitData($initData);
$userId = null;

if ($validatedData !== false) {
    $userId = intval($validatedData['user']['id']);
    error_log("getWithdrawalHistory INFO: initData валидирован для user {$userId}");
} else {
    // Валидация не прошла - возвращаем ошибку
    error_log("getWithdrawalHistory ERROR: Валидация initData не прошла");
    http_response_code(401);
    echo json_encode(['error' => 'Ошибка аутентификации']);
    exit;
}

// 3. Загрузка данных пользователя
$userData = loadUserData();
if (!is_array($userData)) {
    error_log("getWithdrawalHistory ERROR: loadUserData вернул не массив");
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: LD1']);
    exit;
}

// 4. Получение истории выплат пользователя
if (!isset($userData[$userId])) {
    error_log("getWithdrawalHistory ERROR: Пользователь {$userId} не найден");
    http_response_code(404);
    echo json_encode(['error' => 'Ошибка: Пользователь не найден']);
    exit;
}

// Получаем историю выплат из данных пользователя
$withdrawals = isset($userData[$userId]['withdrawals']) ? $userData[$userId]['withdrawals'] : [];

// Сортируем выплаты по времени (от новых к старым)
usort($withdrawals, function($a, $b) {
    return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
});

// 5. Успешный ответ
http_response_code(200);
echo json_encode([
    'withdrawals' => $withdrawals,
    'total_count' => count($withdrawals)
]);
error_log("getWithdrawalHistory INFO: Успешно отправлена история выплат для пользователя {$userId} (всего: " . count($withdrawals) . ")");
?>
