<?php
/**
 * api/cron/send_notifications_fixed.php
 * Исправленная версия cron-скрипта для отправки уведомлений
 * Запускается каждый день в 10:00 UTC
 * Работает с JSON файлами данных пользователей
 */

// Включаем логирование
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Увеличиваем лимиты для работы с большими файлами
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300); // 5 минут

// Логирование начала работы скрипта
$logFile = __DIR__ . '/notifications_fixed.log';
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry;
}

try {
    logMessage("=== НАЧАЛО ОТПРАВКИ УВЕДОМЛЕНИЙ (ИСПРАВЛЕННАЯ ВЕРСИЯ) ===");
    
    // Подключение зависимостей
    logMessage("Подключаем config.php");
    require_once __DIR__ . '/../config.php';
    logMessage("config.php подключен успешно");
    
    logMessage("Подключаем functions.php");
    require_once __DIR__ . '/../functions.php';
    logMessage("functions.php подключен успешно");
    
    // Отладочная информация
    logMessage("PHP версия: " . PHP_VERSION);
    logMessage("Память: " . ini_get('memory_limit') . ", время: " . ini_get('max_execution_time') . "с");
    
    // Проверяем наличие файла данных пользователей
    if (!defined('USER_DATA_FILE') || !file_exists(USER_DATA_FILE)) {
        logMessage("ОШИБКА: Файл данных пользователей не найден");
        if (defined('USER_DATA_FILE')) {
            logMessage("USER_DATA_FILE путь: " . USER_DATA_FILE);
        }
        exit(1);
    }
    
    logMessage("Файл данных найден: " . USER_DATA_FILE);
    logMessage("Размер файла: " . round(filesize(USER_DATA_FILE) / 1024, 2) . " КБ");
    
    // Загружаем данные пользователей
    logMessage("Начинаем загрузку данных пользователей...");
    $userData = loadUserData();
    logMessage("Загрузка данных завершена");
    
    if (!$userData || !is_array($userData)) {
        logMessage("ОШИБКА: Не удалось загрузить данные пользователей");
        logMessage("Тип данных: " . gettype($userData));
        exit(1);
    }
    
    logMessage("Загружено пользователей: " . count($userData));
    
    // === РОТАЦИЯ ЛОГОВ РЕКЛАМНОЙ СТАТИСТИКИ ===
    logMessage("=== НАЧАЛО РОТАЦИИ ЛОГОВ ===");
    
    $rotationScript = __DIR__ . '/log_rotation.php';
    
    if (file_exists($rotationScript)) {
        logMessage("Запускаем скрипт ротации логов");
        
        // Выполняем скрипт ротации
        ob_start();
        include $rotationScript;
        $rotationResult = ob_get_clean();
        
        // Парсим результат
        $result = json_decode($rotationResult, true);
        
        if ($result && $result['success']) {
            logMessage("Ротация логов выполнена успешно:");
            logMessage("- Ротация по размеру: " . ($result['rotated_by_size'] ? 'Да' : 'Нет'));
            logMessage("- Ротация по дате: " . ($result['rotated_by_date'] ? 'Да' : 'Нет'));
            logMessage("- Удалено архивов: " . $result['deleted_archives']);
            logMessage("- Всего архивов: " . $result['archive_stats']['count']);
            logMessage("- Размер архивов: " . round($result['archive_stats']['total_size'] / 1024 / 1024, 2) . " МБ");
        } else {
            $error = $result ? $result['error'] : 'Неизвестная ошибка';
            logMessage("ОШИБКА ротации логов: " . $error);
        }
    } else {
        logMessage("ПРЕДУПРЕЖДЕНИЕ: Скрипт ротации логов не найден: " . $rotationScript);
    }
    
    logMessage("=== ЗАВЕРШЕНИЕ РОТАЦИИ ЛОГОВ ===");
    
    // Здесь будет код отправки уведомлений (пока пропускаем для тестирования)
    logMessage("=== ОТПРАВКА УВЕДОМЛЕНИЙ ПРОПУЩЕНА (ТЕСТОВЫЙ РЕЖИМ) ===");
    
    logMessage("=== ЗАВЕРШЕНИЕ ОТПРАВКИ УВЕДОМЛЕНИЙ ===");
    
} catch (Exception $e) {
    logMessage("КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    logMessage("Стек вызовов: " . $e->getTraceAsString());
    exit(1);
} catch (Error $e) {
    logMessage("ФАТАЛЬНАЯ ОШИБКА: " . $e->getMessage());
    logMessage("Стек вызовов: " . $e->getTraceAsString());
    exit(1);
}

exit(0);
?>
