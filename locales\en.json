{"bot": {"welcome_title": "🎉 Welcome to UniQPaid!", "welcome_subtitle": "💰 Just watch ads now and get crypto instantly!", "welcome_description": "🚀 No need to wait for listing! Instant auto-payouts to your wallet!", "ad_availability_info": "📊 Regularly check the app for new advertising offers. Every day there are 20 pushes, 20 banner ads and 20 videos available.", "withdrawal_warning": "⚠️ Be careful with the withdrawal amount size. Too small orders may get stuck in the blockchain due to minimum limits of exchanges or wallets.\nFor the most accurate information, it is recommended to familiarize yourself with the documentation of the specific wallet or service where you plan to send cryptocurrency.", "how_it_works": "📊 How it works:", "earn_coins": "• Watch ads and get up to 10 coins for each view", "invite_friends": "• Invite friends and get 10% of their earnings for lifetime", "withdraw_crypto": "• Withdraw earned funds to crypto wallets", "exchange_rate": "💎 Rate: 10 coins = $0.01 USD", "start_earning": "🚀 Start earning right now!", "launch_app": "🚀 Launch App", "my_balance": "💰 My Balance", "balance": "💰 Your Balance", "friends": "👥 Friends", "statistics": "📊 Statistics", "help": "❓ Help", "back": "🔙 Back", "hello": "👋 Hello, {name}!", "choose_action": "Choose an action:", "your_balance": "💰 Your Balance", "current_balance": "🪙 Current balance: {balance} coins", "balance_usd": "💵 In dollars: ${amount} USD", "total_earned": "📈 Personal earnings: {amount} coins", "from_referrals": "👥 From referrals: {amount} coins", "withdrawals_count": "💸 Withdrawals: {count}", "open_app_withdraw": "🚀 Open the app to withdraw funds!", "referral_program": "👥 Referral Program", "earn_percent": "🎁 Earn 10% from each invited friend's earnings! No limit on the number of invited friends!", "your_stats": "📊 Your statistics:", "invited_friends": "👥 Friends invited: {count}", "earned_from_refs": "💰 Earned from referrals: {amount} coins", "your_ref_link": "🔗 Your referral link:", "share_link": "📤 Share the link with friends and earn together!", "share_button": "📤 Share Link", "share_text": "🎉 Join <PERSON> and earn coins for watching ads! Instant withdrawal!", "your_statistics": "📊 Your Statistics", "withdrawals_made": "💸 Withdrawals made: {count}", "days_active": "📅 Days in system: {days}", "avg_per_day": "⚡ Average per day: {amount} coins", "registration_date": "📅 Registration date: {date}", "help_title": "❓ UniQPaid Help", "how_to_earn": "🎯 How to earn:", "open_miniapp": "• Open the mini-app", "watch_ads": "• Watch ads and get up to 10 coins per each view", "invite_earn": "• Invite friends and get 10% of their earnings", "how_to_withdraw": "💰 How to withdraw:", "accumulate_coins": "• Accumulate coins", "open_app_help": "• Open the app", "go_to_earnings": "• Go to 'Earnings' section", "request_withdrawal": "• Request withdrawal to crypto wallet", "exchange_rate_help": "💎 Rate: 10 coins = $0.01 USD", "support": "📞 Support: @support_uniqpaid", "user_not_found": "❌ User not found. Send /start to register.", "unknown_command": "Unknown command."}, "app": {"title": "UniQPaid", "nav": {"home": "Home", "earnings": "Earnings", "friends": "Friends"}, "currency": {"coins": "coins"}, "common": {"important": "Important", "loading": "Loading...", "success": "Success!", "excellent": "Excellent!", "error": "Error", "warning": "Warning", "info": "Information", "ok": "OK", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "status": "Status", "processing": "Processing", "sending": "Sending...", "test_user": "Test User", "offline_mode": "Offline mode"}, "tasks": {"title": "Tasks", "open_link": "Open Link", "watch_video": "Watch Video", "watch_ad": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ad_views_left": "{count} ad views left"}, "earnings": {"title": "Earnings", "your_balance": "Your Balance", "available_for_withdrawal": "Available for withdrawal: <span id=\"available-withdrawal\">{amount}</span> coins.", "calculator_title": "Withdrawal Calculator", "exchange_rate": "Rate: 1 coin = $0.001", "amount_to_withdraw": "Amount to withdraw", "enter_coins_amount": "Enter number of coins", "minimum": "Minimum withdrawal amount", "minimum_withdrawal": "Minimum withdrawal amount", "withdrawal_request": "Withdrawal Request", "withdrawal_hint": "Select currency from calculator above and specify wallet address.", "withdrawal_amount": "Withdrawal amount", "withdrawal_amount_help": "Your earnings will be automatically converted to the selected cryptocurrency at the current Binance rate. 1 coin = $0.001", "enter_amount": "Enter amount", "choose_crypto": "Choose cryptocurrency", "crypto_amount": "Amount to receive in cryptocurrency", "crypto_amount_placeholder": "Will be calculated automatically", "wallet_address": "Wallet address", "selected_crypto": "Selected cryptocurrency", "enter_wallet_address": "Enter payment address", "request_withdrawal": "Request Withdrawal", "wallet_warning": "Make sure the wallet address is correct. Funds will be sent to the specified address and cannot be returned in case of error.", "withdrawal_history": "Withdrawal History", "no_withdrawals": "No withdrawals yet", "insufficient_balance": "Insufficient balance", "min_withdrawal": "Minimum withdrawal amount: {amount} coins", "wallet_address_short": "Wallet address is too short", "enter_correct_amount": "Enter correct withdrawal amount", "enter_amount_for_calculation": "Enter amount for calculation", "amount_to_withdraw_coins": "Amount to withdraw (coins)", "amount_to_receive": "Amount to receive", "enter_amount_for_withdrawal": "Enter amount for withdrawal", "form_filled_correctly": "Form filled correctly", "withdrawal_request_sent": "✅ <PERSON><PERSON><PERSON> request sent successfully!", "withdrawal_error": "Error", "unknown_error": "Unknown error", "request_send_error": "❌ Request sending error", "insufficient_balance_have": "Insufficient funds (available: {balance})", "minimum_with_fee": "Minimum: {amount} (with fee)", "minimum_amount_simple": "Minimum: {amount}", "ready_for_withdrawal": "Ready for withdrawal", "insufficient_funds_need": "Insufficient funds! Need: {amount} coins", "insufficient_balance_need": "Insufficient funds (need: {amount})", "calculation_error": "Calculation error", "insufficient_for_withdrawal": "Insufficient for withdrawal", "wait_calculation": "Waiting for calculation", "need": "Need", "minimum_amount": "Minimum: {amount} coins ({usd})", "ready_for_withdrawal_amount": "Ready for withdrawal! You'll get {amount}", "fee_exceeds_amount": "Fee exceeds withdrawal amount", "insufficient_funds_with_fee": "Insufficient funds including fee. Required: {required} coins (balance: {balance}, fee: {fee})", "increase_withdrawal_amount": "Enter a larger withdrawal amount.", "fee_changed_recalculate": "The payment service fee has changed! Please check and recalculate the withdrawal amount.", "fee_exceeds_amount_detailed": "Fee ({fee}) exceeds withdrawal amount ({amount})", "ads_ready": "Ads ready!", "initialization_error": "Ad initialization error", "update_error": "Ad update error", "failed_to_show": "Failed to show ad. Try again later.", "network_problem": "Network connection problem", "currently_unavailable": "Ads currently unavailable", "best_choice": "Best choice", "good": "Good", "expensive": "Expensive", "withdrawal_recommendations_tooltip": "Different exchanges and exchange services have their own minimum limits for crediting funds. Make sure the withdrawal amount exceeds the minimum requirements of the receiving party, otherwise the payment may get stuck or not reach the recipient. We recommend withdrawing amounts from $5 and above for reliable receipt of funds.", "withdrawal_amount_calc": "Withdrawal amount", "network_fee": "Network fee", "final_amount": "You will receive", "efficiency": "Efficiency", "withdrawal_history_desc": "Here you can see the history of all your withdrawals and their statuses.", "refresh_history": "Refresh History", "min_balance_required": "Minimum balance for withdrawal: {amount} coins", "min_withdrawal_amount": "Minimum withdrawal amount: {amount} coins", "invalid_wallet_address": "Invalid wallet address", "form_valid": "Form filled correctly", "withdrawal_success": "With<PERSON>wal request created successfully!", "withdrawal_created": "Request created!", "withdrawal_success_message": "Your withdrawal request for {amount} coins has been successfully submitted. Track the status in the \"Withdrawal History\" section.", "available_for_withdrawal_status": "✅ Available for withdrawal", "below_minimum_amount": "⚠️ Below minimum amount", "below_minimum": "Below minimum", "insufficient_funds": "Insufficient funds", "amount_missing": "Missing", "insufficient_available_balance": "Insufficient. Available for withdrawal: {balance} coins", "sufficient_balance": "✅ Sufficient coins", "withdrawal_recommendations": "Withdrawal Recommendations", "withdrawal_history_title": "Withdrawal History", "withdrawal_history_description": "Here you can see the history of all your withdrawals and their statuses."}, "currencies": {"ethereum": "Ethereum", "bitcoin": "Bitcoin", "usdt": "USDT", "ton": "TON"}, "friends": {"title": "Friends and Invitations", "share_app": "Share", "share_button": "Share", "invite_friend": "Referral Link", "referral_description": "Share the link. You will receive 10% of the earnings of friends who come through it!", "referral_stats": "Statistics", "total_referrals": "Total referrals:", "earned_from_referrals": "Earned from referrals:", "refresh_stats": "Refresh Statistics", "subscriptions": "Subscriptions", "no_referrals": "You don't have any referrals yet. Invite friends!", "loading_info": "Loading information...", "invite_friends_hint": "Invite friends and start earning!", "referrer_description": "Information about the user who invited you to the app.", "link_copied": "Link copied!", "balance": "Balance", "invited_you": "This user invited you to the app", "no_referrer": "You don't have a referrer", "joined_independently": "You joined the app independently", "stats_updated": "Statistics updated", "share_title": "Share", "share_description": "Tell your friends about this cool cyber-punk app and earn together!", "referral_link_title": "Referral Link", "referral_link_description": "Share your unique link and get 10% from each invited friend's earnings!", "my_referrer_title": "<PERSON> Referrer", "share_text": "Just watch ads and get crypto to your card. Instant withdrawal to wallet"}, "validation": {"invalid_ton_address": "Invalid address. Use TON address (UQ, EQ or kQ)", "invalid_eth_address": "Invalid Ethereum address format. Must start with 0x", "invalid_tron_address": "Invalid TRON address format. Must start with T", "invalid_btc_address": "Invalid Bitcoin address format. Must start with 1, 3 or bc1", "invalid_ltc_address": "Invalid Litecoin address format. Must start with L, M or ltc1", "invalid_bch_address": "Invalid Bitcoin Cash address format. Must start with q, p, 1 or 3", "invalid_xrp_address": "Invalid Ripple address format. Must start with r", "invalid_ada_address": "Invalid Cardano address format. Must start with addr1", "invalid_dot_address": "Invalid Polkadot address format. Must start with 1", "invalid_address": "Invalid wallet address format"}, "status": {"waiting_initialization": "Waiting for initialization...", "initializing_app": "Initializing application...", "loading_modules": "Loading modules...", "loading_components": "Loading additional components...", "app_ready": "Application ready to work", "updating_ads": "Updating ad blocks...", "loading": "Loading...", "generating_link": "Generating link...", "loading_referrer_info": "Loading referrer information...", "checking_who_invited": "Checking who invited you", "loading_info": "Loading information...", "ready": "Ready", "countdown_finished": "Ready", "detecting_language": "Detecting language...", "authentication_error": "Authentication error", "stats_updated": "Statistics updated", "initializing_ads": "Initializing ads..."}, "placeholders": {"enter_coins_amount": "Enter number of coins", "enter_amount": "Enter amount", "enter_wallet_address": "Enter address", "auto_calculated": "Will be calculated automatically", "ton_address": "Enter wallet address", "eth_address": "Enter wallet address", "btc_address": "Enter wallet address", "usdt_trc20_address": "Enter wallet address", "tron_address": "Enter wallet address"}, "buttons": {"share": "Share", "copy": "Copy", "refresh_stats": "Refresh Statistics", "refresh_history": "Refresh History", "request_withdrawal": "Request Withdrawal", "calculator_tab": "Withdrawal Calculator", "withdrawal_tab": "Withdrawal Request"}, "tooltips": {"balance": "Balance", "copy_referral": "Copy", "show_hint": "Show hint"}, "empty_states": {"history_empty_title": "Withdrawal history is empty", "history_empty_description": "Your withdrawal requests will be displayed here", "history_empty_hint": "Go to calculator to calculate and request your first withdrawal", "no_referrals": "You don't have any referrals yet. Invite friends!", "no_referrer": "You don't have a referrer", "no_referrer_desc": "You joined the app independently"}, "labels": {"current_balance": "Current account balance and available withdrawal funds.", "available_withdrawal": "Available for withdrawal:", "your_balance_label": "Your balance:", "selected_crypto": "Selected cryptocurrency:", "withdrawal_amount_coins": "Withdrawal amount (coins):", "amount_to_receive": "Amount to receive:", "wallet_address": "Wallet address:", "minimum_amount_label": "Minimum amount:", "withdrawal_history_desc": "Here you can see the history of all your withdrawals and their statuses.", "balance_description": "Current account balance and available withdrawal funds.", "calculator_description": "Calculate withdrawal amount in various cryptocurrencies", "withdrawal_form_description": "Select currency from calculator above and specify wallet address for receiving funds."}, "ads": {"banner_reward_credited": "Ad watching reward credited!", "video_reward_credited": "Video watching reward credited!", "ad_reward_credited": "Ad pushing reward credited!", "no_ads_available": "No banners available. Try again later.", "ad_loading": "Loading ads...", "ad_ready": "Ad ready", "cooldown_active": "Cooldown active", "requesting_banner_redirect": "Requesting auto ad...", "requesting_video": "Requesting video ad...", "requesting_interstitial": "Requesting interstitial banner...", "requesting_ad": "Requesting ad...", "loading_ad_module": "Loading ad module...", "wait_ad_showing": "Please wait, ad is already showing...", "wait_before_next": "Please wait before next viewing...", "crediting_reward": "Crediting reward...", "refreshing_ads": "Refreshing ads...", "ads_unavailable": "Ads unavailable", "module_not_loaded": "Ad module not loaded. Try restarting the application.", "reward_credited_balance": "Reward credited! Balance: {balance}", "reward_status_credited": "🎉 Reward credited: +{reward} {coins}!", "sdk_error": "SDK Error: {error}", "failed_to_show": "Failed to show ad. Please try again later.", "network_problem": "Network connection problem", "timeout_error": "Ad network response timeout exceeded", "unknown_error": "Unknown error", "unavailable_or_closed": "Ad unavailable or was closed", "ads_available_again": "Ads available again!", "ads_updated": "Ads updated!", "update_error": "Ad update error"}, "withdrawal_status": {"waiting": "Waiting for processing", "processing": "Processing", "sending": "Sending to wallet", "finished": "Sent to wallet", "completed": "Sent to wallet", "confirmed": "Confirmed in blockchain", "failed": "Payment error", "rejected": "Rejected by system", "pending": "Processing", "cancelled": "Cancelled", "expired": "Expired"}, "currency_status": {"best": "Best choice", "good": "Good", "expensive": "Expensive", "available": "Available"}}}