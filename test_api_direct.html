<!DOCTYPE html>
<html>
<head>
    <title>Тест API логирования</title>
</head>
<body>
    <h1>Тест API логирования</h1>
    <button onclick="testLogAdClick()">Тест logAdClick API</button>
    <div id="result"></div>

    <script>
    async function testLogAdClick() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = 'Отправляем запрос...';

        // Создаем правильные тестовые данные
        const testUser = JSON.stringify({id: 12345, first_name: "Test", username: "testuser"});
        const initData = `user=${encodeURIComponent(testUser)}&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test`;

        console.log('Отправляем initData:', initData);

        try {
            console.log('Отправляем данные:', {
                initData: initData,
                adType: 'interstitial',
                status: 'button_click'
            });

            const response = await fetch('api/logAdClick.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    initData: initData,
                    adType: 'interstitial',
                    clickType: 'button_click'
                })
            });

            const result = await response.text();
            console.log('Ответ API:', result);
            console.log('Статус ответа:', response.status);

            if (response.ok) {
                resultDiv.innerHTML = `✅ Успех! Ответ: ${result}`;
            } else {
                resultDiv.innerHTML = `❌ Ошибка ${response.status}: ${result}`;
            }
        } catch (error) {
            console.error('Ошибка:', error);
            resultDiv.innerHTML = `❌ Ошибка: ${error.message}`;
        }
    }
    </script>
</body>
</html>
