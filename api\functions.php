<?php
/**
 * functions.php
 * Основные функции для работы с данными пользователей
 */

// Путь к файлу данных пользователей (если не определен в config.php)
if (!defined('USER_DATA_FILE')) {
    define('USER_DATA_FILE', __DIR__ . '/user_data.json');
}

/**
 * Загружает данные пользователей из JSON файла
 */
function loadUserData() {
    if (!file_exists(USER_DATA_FILE)) {
        error_log("functions.php WARNING: Файл user_data.json не найден");
        return [];
    }
    
    $content = file_get_contents(USER_DATA_FILE);
    if ($content === false) {
        error_log("functions.php ERROR: Не удалось прочитать user_data.json");
        return false;
    }
    
    $data = json_decode($content, true);
    if ($data === null) {
        error_log("functions.php ERROR: Ошибка парсинга JSON в user_data.json");
        return false;
    }
    
    return $data;
}

/**
 * Сохраняет данные пользователей в JSON файл
 */
function saveUserData($userData) {
    if (!is_array($userData)) {
        error_log("functions.php ERROR: Данные для сохранения должны быть массивом");
        return false;
    }
    
    $json = json_encode($userData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        error_log("functions.php ERROR: Ошибка кодирования JSON");
        return false;
    }
    
    // Создаем backup перед сохранением
    if (file_exists(USER_DATA_FILE)) {
        $backupFile = USER_DATA_FILE . '.backup.' . date('Y-m-d-H-i-s');
        copy(USER_DATA_FILE, $backupFile);
    }
    
    $result = file_put_contents(USER_DATA_FILE, $json, LOCK_EX);
    if ($result === false) {
        error_log("functions.php ERROR: Не удалось сохранить user_data.json");
        return false;
    }
    
    error_log("functions.php INFO: Данные пользователей сохранены (" . strlen($json) . " байт)");
    return true;
}

/**
 * Получает данные конкретного пользователя
 */
function getUserData($userId) {
    $userData = loadUserData();
    if (!$userData) {
        return false;
    }
    
    return $userData[$userId] ?? null;
}

/**
 * Сохраняет данные конкретного пользователя
 */
function saveUserDataById($userId, $userInfo) {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    $userData[$userId] = $userInfo;
    return saveUserData($userData);
}

/**
 * Добавляет выплату пользователю
 */
function addUserWithdrawal($userId, $withdrawalData) {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    if (!isset($userData[$userId])) {
        error_log("functions.php ERROR: Пользователь {$userId} не найден");
        return false;
    }
    
    // Инициализируем массив выплат если его нет
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }
    
    // Добавляем выплату
    $userData[$userId]['withdrawals'][] = $withdrawalData;
    
    // Обновляем счетчик выплат
    $userData[$userId]['withdrawals_count'] = count($userData[$userId]['withdrawals']);
    
    // Добавляем в лог выплат
    if (!isset($userData[$userId]['withdrawal_log'])) {
        $userData[$userId]['withdrawal_log'] = [];
    }
    $userData[$userId]['withdrawal_log'][] = time();
    
    return saveUserData($userData);
}

/**
 * Обновляет баланс пользователя
 */
function updateUserBalance($userId, $newBalance) {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    if (!isset($userData[$userId])) {
        error_log("functions.php ERROR: Пользователь {$userId} не найден");
        return false;
    }
    
    $oldBalance = $userData[$userId]['balance'] ?? 0;
    $userData[$userId]['balance'] = $newBalance;
    
    error_log("functions.php INFO: Баланс пользователя {$userId}: {$oldBalance} -> {$newBalance}");
    
    return saveUserData($userData);
}

/**
 * Списывает монеты с баланса пользователя
 */
function deductUserBalance($userId, $amount) {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    if (!isset($userData[$userId])) {
        error_log("functions.php ERROR: Пользователь {$userId} не найден");
        return false;
    }
    
    $currentBalance = $userData[$userId]['balance'] ?? 0;
    
    if ($currentBalance < $amount) {
        error_log("functions.php ERROR: Недостаточно средств у пользователя {$userId}: {$currentBalance} < {$amount}");
        return false;
    }
    
    $newBalance = $currentBalance - $amount;
    $userData[$userId]['balance'] = $newBalance;
    
    error_log("functions.php INFO: Списано {$amount} монет у пользователя {$userId}: {$currentBalance} -> {$newBalance}");
    
    return saveUserData($userData);
}

/**
 * Возвращает монеты на баланс пользователя
 */
function refundUserBalance($userId, $amount, $reason = 'refund') {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    if (!isset($userData[$userId])) {
        error_log("functions.php ERROR: Пользователь {$userId} не найден");
        return false;
    }
    
    $currentBalance = $userData[$userId]['balance'] ?? 0;
    $newBalance = $currentBalance + $amount;
    $userData[$userId]['balance'] = $newBalance;
    
    error_log("functions.php INFO: Возвращено {$amount} монет пользователю {$userId} ({$reason}): {$currentBalance} -> {$newBalance}");
    
    return saveUserData($userData);
}

/**
 * Получает историю выплат пользователя
 */
function getUserWithdrawals($userId) {
    $userData = loadUserData();
    if (!$userData || !isset($userData[$userId])) {
        return [];
    }
    
    return $userData[$userId]['withdrawals'] ?? [];
}

/**
 * Обновляет статус выплаты
 */
function updateWithdrawalStatus($userId, $withdrawalIndex, $newStatus, $additionalData = []) {
    $userData = loadUserData();
    if ($userData === false) {
        return false;
    }
    
    if (!isset($userData[$userId]['withdrawals'][$withdrawalIndex])) {
        error_log("functions.php ERROR: Выплата не найдена: пользователь {$userId}, индекс {$withdrawalIndex}");
        return false;
    }
    
    $oldStatus = $userData[$userId]['withdrawals'][$withdrawalIndex]['status'] ?? 'unknown';
    $userData[$userId]['withdrawals'][$withdrawalIndex]['status'] = $newStatus;
    $userData[$userId]['withdrawals'][$withdrawalIndex]['updated_at'] = date('Y-m-d H:i:s');
    
    // Добавляем дополнительные данные
    foreach ($additionalData as $key => $value) {
        $userData[$userId]['withdrawals'][$withdrawalIndex][$key] = $value;
    }
    
    error_log("functions.php INFO: Статус выплаты обновлен: пользователь {$userId}, {$oldStatus} -> {$newStatus}");
    
    return saveUserData($userData);
}

/**
 * Находит выплату по payout_id
 */
function findWithdrawalByPayoutId($payoutId) {
    $userData = loadUserData();
    if (!$userData) {
        return null;
    }
    
    foreach ($userData as $userId => $user) {
        if (!isset($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            if (($withdrawal['payout_id'] ?? null) === $payoutId || ($withdrawal['id'] ?? null) === $payoutId) {
                return [
                    'user_id' => $userId,
                    'index' => $index,
                    'withdrawal' => $withdrawal
                ];
            }
        }
    }
    
    return null;
}

/**
 * Логирование с timestamp
 */
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$level}: {$message}";
    error_log($logMessage);
    
    // Также записываем в отдельный файл если нужно
    $logFile = __DIR__ . '/withdrawal.log';
    file_put_contents($logFile, $logMessage . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Проверяет целостность данных пользователя
 */
function validateUserData($userData) {
    $errors = [];
    
    foreach ($userData as $userId => $user) {
        // Проверяем обязательные поля
        if (!isset($user['balance'])) {
            $errors[] = "Пользователь {$userId}: отсутствует поле balance";
        }
        
        // Проверяем выплаты
        if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
            foreach ($user['withdrawals'] as $index => $withdrawal) {
                if (!isset($withdrawal['id']) && !isset($withdrawal['payout_id'])) {
                    $errors[] = "Пользователь {$userId}, выплата {$index}: отсутствует ID";
                }
                
                if (!isset($withdrawal['status'])) {
                    $errors[] = "Пользователь {$userId}, выплата {$index}: отсутствует статус";
                }
                
                if (!isset($withdrawal['coins_amount'])) {
                    $errors[] = "Пользователь {$userId}, выплата {$index}: отсутствует сумма";
                }
            }
        }
    }
    
    return $errors;
}
?>
