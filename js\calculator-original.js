// === calculator-original.js ===
// Файл: js/calculator-original.js
// Описание: Управляет калькулятором вывода с полной логикой.

class OriginalCalculatorManager {
  constructor() {
    this.isInitialized = false;
    this.currencyData = {};
    this.selectedCurrency = 'ton';

    // Элементы интерфейса (из оригинала)
    this.elements = {
      calcAmountInput: document.getElementById('calc-amount'),
      calcBalance: document.getElementById('calc-balance'),
      dollarEquivalent: document.getElementById('dollar-equivalent'),
      balanceCheck: document.getElementById('balance-check'),
      currencyTabs: document.querySelectorAll('.currency-tab'),
      withdrawalAmountInput: document.getElementById('withdrawal-amount'),
      withdrawalAmountDisplay: document.getElementById('withdrawal-amount-display'),
      finalAmountDisplay: document.getElementById('final-amount-display'),
      efficiencyDisplay: document.getElementById('efficiency-display'),
      currencyCardName: document.getElementById('currency-card-name'),
      currencyCardIcon: document.getElementById('currency-card-icon'),
      warningSection: document.getElementById('warning-section'),
      minimumInfoItem: document.getElementById('minimum-info-item'),
      missingInfoItem: document.getElementById('missing-info-item'),
      minimumRequiredInfo: document.getElementById('minimum-required-info'),
      amountMissingInfo: document.getElementById('amount-missing-info')
    };

    // ИСПРАВЛЕНИЕ: Проверяем что табы найдены
    console.log('[Calculator] Найдено табов валют:', this.elements.currencyTabs.length);
    this.elements.currencyTabs.forEach((tab, index) => {
      console.log(`[Calculator] Таб ${index}: ${tab.dataset.currency}`);
    });
  }

  async init() {
    if (this.isInitialized) {
      console.log('[Calculator] Уже инициализирован.');
      return;
    }
    console.log('[Calculator] Инициализация калькулятора вывода...');

    if (!this.elements.calcAmountInput) {
      console.warn('[Calculator] Элемент calc-amount не найден');
      return;
    }

    await this.loadCurrencyData();
    this.initializeCurrencyTabs();
    this.setupEventListeners();
    this.updateBalanceDisplay();
    this.updateCalculatorDisplay(0); // Показываем пустое состояние
    this.updateFeeDisplay();
    this.updateMinimumDisplay();

    this.isInitialized = true;
  }

  async loadCurrencyData() {
    try {
      console.log('[Calculator] Загрузка данных валют...');

      // ИСПРАВЛЕНИЕ: Используем единый менеджер данных валют
      if (window.currencyDataManager) {
        this.currencyData = window.currencyDataManager.getCurrencyData();
        console.log('[Calculator] Данные валют загружены из CurrencyDataManager');
        return;
      }

      // ИСПРАВЛЕНИЕ: Загружаем данные из кэша или используем fallback (из оригинала)
      try {
        // Пытаемся загрузить данные из кэша
        const response = await fetch(`${window.API_BASE_URL || ''}/getCachedCurrencyData.php`);
        if (response.ok) {
          const cachedData = await response.json();
          if (cachedData.success && cachedData.currencies) {
            console.log('[Calculator] Данные валют загружены из кэша');
            this.currencyData = cachedData.currencies;

            // Обновляем CurrencyDataManager если он доступен
            if (window.currencyDataManager) {
              window.currencyDataManager.updateCurrencyData(cachedData.currencies);
            }
            return;
          }
        }
      } catch (error) {
        console.warn('[Calculator] Ошибка загрузки кэшированных данных:', error);
      }

      // ИСПРАВЛЕНИЕ: Fallback данные - используем данные из CurrencyDataManager
      if (window.currencyDataManager) {
        this.currencyData = window.currencyDataManager.getCurrencyData();
      } else {
        // Последний fallback если CurrencyDataManager недоступен
        this.currencyData = {
          ton: { name: 'TON (Telegram)', minCoins: window.appSettings ? window.appSettings.get('min_withdrawal_amount') || 1000 : 1000, networkFee: 0.15, status: 'best' },
          eth: { name: 'Ethereum (ETH)', minCoins: window.appSettings ? window.appSettings.get('min_withdrawal_amount') || 3000 : 3000, networkFee: 2.5, status: 'expensive' },
          btc: { name: 'Bitcoin (BTC)', minCoins: window.appSettings ? window.appSettings.get('min_withdrawal_amount') || 5000 : 5000, networkFee: 4.0, status: 'expensive' },
          usdttrc20: { name: 'USDT (TRC20)', minCoins: window.appSettings ? window.appSettings.get('min_withdrawal_amount') || 2000 : 2000, networkFee: 1.0, status: 'good' },
          trx: { name: 'TRON (TRX)', minCoins: window.appSettings ? window.appSettings.get('min_withdrawal_amount') || 1500 : 1500, networkFee: 0.5, status: 'good' }
        };
      }

      // Пробуем загрузить актуальные данные с сервера
      if (window.appSettings && window.appSettings.getCurrencyData) {
        const serverData = window.appSettings.getCurrencyData();
        if (serverData) {
          Object.assign(this.currencyData, serverData);

          // Обновляем CurrencyDataManager
          if (window.currencyDataManager) {
            window.currencyDataManager.updateCurrencyData(serverData);
          }
        }
      }

    } catch (error) {
      console.error('[Calculator] Ошибка загрузки данных валют:', error);
    }
  }

  setupEventListeners() {
    console.log('[Calculator] Настройка обработчиков событий...');

    // Обработчик изменения суммы (из оригинала)
    if (this.elements.calcAmountInput) {
      this.elements.calcAmountInput.addEventListener('input', () => {
        const amount = parseInt(this.elements.calcAmountInput.value) || 0;
        this.updateCalculatorDisplay(amount);
        this.updateBalanceCheck(amount);

        // ИСПРАВЛЕНИЕ: Синхронизируем с формой выплат (из оригинала)
        if (this.elements.withdrawalAmountInput && document.activeElement !== this.elements.withdrawalAmountInput) {
          this.elements.withdrawalAmountInput.value = amount;

          // ИСПРАВЛЕНИЕ: Обновляем криптосумму в форме выплат
          if (window.withdrawalFormManager) {
            if (window.withdrawalFormManager.updateCryptoAmountField) {
              window.withdrawalFormManager.updateCryptoAmountField();
            }
            // ИСПРАВЛЕНИЕ: Валидируем форму после изменения
            if (window.withdrawalFormManager.validateForm) {
              window.withdrawalFormManager.validateForm();
            }
          }
        }
      });
      console.log('[Calculator] ✅ Обработчик ввода суммы установлен');
    } else {
      console.warn('[Calculator] ❌ Поле ввода суммы не найдено');
    }

    // ИСПРАВЛЕНИЕ: Переинициализируем табы если их нет
    if (this.elements.currencyTabs.length === 0) {
      console.warn('[Calculator] Табы не найдены, переинициализируем...');
      this.elements.currencyTabs = document.querySelectorAll('.currency-tab');
      console.log('[Calculator] После переинициализации найдено табов:', this.elements.currencyTabs.length);
    }

    // Обработчики для выбора валюты (табы всегда активны)
    if (this.elements.currencyTabs.length > 0) {
      this.elements.currencyTabs.forEach((tab, index) => {
        const currency = tab.dataset.currency;
        console.log(`[Calculator] Настройка обработчика для таба ${index}: ${currency}`);

        tab.addEventListener('click', (e) => {
          e.preventDefault();
          console.log(`[Calculator] Клик по табу: ${currency}`);
          this.selectCurrencyTab(currency);

          // Обновляем расчеты при смене валюты
          if (this.elements.calcAmountInput && this.elements.calcAmountInput.value) {
            const amount = parseInt(this.elements.calcAmountInput.value) || 0;
            this.updateCalculatorDisplay(amount);
          } else {
            // Обновляем предупреждение даже если сумма не введена
            this.updateMinimumWarning(0, currency);
          }
        });
      });
      console.log('[Calculator] ✅ Обработчики табов установлены');
    } else {
      console.error('[Calculator] ❌ Табы валют не найдены!');
    }
  }

  /**
   * Инициализирует табы валют (из оригинала)
   */
  initializeCurrencyTabs() {
    // ИСПРАВЛЕНИЕ: Устанавливаем TON как активную валюту по умолчанию
    this.selectedCurrency = 'ton';

    // ИСПРАВЛЕНИЕ: Активируем таб TON по умолчанию
    this.elements.currencyTabs.forEach(tab => {
      tab.classList.remove('active');
      if (tab.dataset.currency === 'ton') {
        tab.classList.add('active');
        console.log('[Calculator] ✅ Таб TON активирован по умолчанию');
      }
    });

    // ИСПРАВЛЕНИЕ: Обновляем карточку валюты для TON
    this.updateCurrencyCardIcon('ton');
    this.updateCurrencyStatus('ton');

    console.log('[Calculator] Валюта по умолчанию установлена: TON (с активацией таба)');
  }

  /**
   * Выбирает таб валюты (из оригинала)
   */
  selectCurrencyTab(currencyCode) {
    this.selectedCurrency = currencyCode;
    console.log(`[Calculator] Выбрана валюта: ${currencyCode}`);

    // ИСПРАВЛЕНИЕ: Активируем табы только если калькулятор активен
    const calculatorSection = document.getElementById('calculator-section');
    if (calculatorSection && !calculatorSection.classList.contains('hidden')) {
      // Обновляем активный таб только если калькулятор показан
      this.elements.currencyTabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.currency === currencyCode) {
          tab.classList.add('active');
        }
      });
      console.log(`[Calculator] Табы валют активированы для ${currencyCode}`);
    } else {
      console.log(`[Calculator] Калькулятор скрыт, не активируем табы валют`);
    }

    // ИСПРАВЛЕНИЕ: Сразу обновляем иконку при переключении таба
    this.updateCurrencyCardIcon(currencyCode);

    // Обновляем информацию о валюте
    this.updateCurrencyInfo(currencyCode);

    // Синхронизируем с формой вывода
    const cryptoCurrencySelect = document.getElementById('crypto-currency');
    if (cryptoCurrencySelect && cryptoCurrencySelect.value !== currencyCode) {
      cryptoCurrencySelect.value = currencyCode;
      if (window.withdrawalFormManager && window.withdrawalFormManager.updateAddressPlaceholder) {
        window.withdrawalFormManager.updateAddressPlaceholder();
      }
    }
  }

  /**
   * Обновляет баланс в калькуляторе (из оригинала)
   */
  updateBalanceDisplay() {
    if (this.elements.calcBalance) {
      const balance = window.balanceManager ? window.balanceManager.getBalance() : 0;
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      this.elements.calcBalance.innerHTML = `${balance.toLocaleString()} <span data-translate="currency.coins">${coinsText}</span>`;
    }
  }

  /**
   * Обновляет отображение калькулятора для выбранной валюты (из оригинала)
   */
  updateCalculatorDisplay(coinAmount) {
    const coinValue = window.appSettings ? window.appSettings.getCoinValue() : 0.001;
    const dollarAmount = coinAmount * coinValue; // 1 монета = $0.001

    if (this.elements.dollarEquivalent) {
      this.elements.dollarEquivalent.textContent = `= $${this.formatCurrency(dollarAmount)}`;
    }

    // Получаем активную валюту
    const activeTab = document.querySelector('.currency-tab.active');
    if (!activeTab) return;

    const currency = activeTab.dataset.currency;
    const data = this.currencyData[currency];
    if (!data) return;

    // Обновляем карточку валюты с расчетами
    this.updateCurrencyCardWithCalculation(currency, coinAmount, dollarAmount);

    // Обновляем все элементы карточки валюты
    this.updateCurrencyCardElements(currency, coinAmount, dollarAmount);

    // Обновляем предупреждение о минимальной сумме
    this.updateMinimumWarning(coinAmount, currency);
  }

  /**
   * Обновляет все элементы карточки валюты (из оригинала)
   */
  updateCurrencyCardElements(currency, coinAmount, dollarAmount) {
    const data = this.currencyData[currency];
    if (!data) return;

    // Обновляем название валюты
    if (this.elements.currencyCardName) {
      this.elements.currencyCardName.textContent = data.name;
    }

    // Обновляем иконку валюты
    this.updateCurrencyCardIcon(currency);

    // Обновляем requirement-value элементы
    const requirementValues = document.querySelectorAll('.requirement-value');
    if (requirementValues.length >= 5) {
      const userBalance = window.balanceManager ? window.balanceManager.getBalance() : 0;

      // Проверяем условия для отображения области "Вы получите"
      const hasMinimumAmount = coinAmount >= data.minCoins;
      const hasSufficientBalance = coinAmount <= userBalance;
      this.updateResultSectionVisibility(coinAmount, hasMinimumAmount, hasSufficientBalance);

      // 0: Минимум к выводу
      const minCoins = data.minCoins;
      const minUsd = (minCoins * (window.appSettings ? window.appSettings.getCoinValue() : 0.001)).toFixed(2);
      const coinsText = window.appLocalization ?
        window.appLocalization.get('currency.coins') :
        'монет';
      requirementValues[0].innerHTML = `${minCoins.toLocaleString()} ${coinsText}<br>($${minUsd})`;

      // 1: Сумма к выводу
      if (coinAmount > 0) {
        const coinsText2 = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        requirementValues[1].innerHTML = `${coinAmount.toLocaleString()} ${coinsText2}<br>($${dollarAmount.toFixed(2)})`;
      } else {
        requirementValues[1].textContent = '-';
      }

      // 2: Сетевая комиссия
      requirementValues[2].textContent = `$${data.networkFee.toFixed(2)}`;

      // 3 и 4: Вы получите и Эффективность - обновляем через расчет
      this.updateFinalAmountAndEfficiency(requirementValues, coinAmount, dollarAmount, data, userBalance);
    }

    // Обновляем статус действия
    this.updateActionStatus(coinAmount, data);
  }

  /**
   * ИСПРАВЛЕНИЕ: Обновляет финальную сумму и эффективность с криптосуммой
   */
  async updateFinalAmountAndEfficiency(requirementValues, coinAmount, dollarAmount, data, userBalance) {
    if (coinAmount === 0) {
      requirementValues[3].textContent = '-';
      requirementValues[4].textContent = '-';
    } else if (coinAmount > userBalance) {
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds') :
        'Недостаточно средств';
      requirementValues[3].textContent = insufficientText;
      requirementValues[4].textContent = '-';
    } else if (coinAmount < data.minCoins) {
      const belowMinText = window.appLocalization ?
        window.appLocalization.get('earnings.below_minimum') :
        'Меньше минимума';
      requirementValues[3].textContent = belowMinText;
      requirementValues[4].textContent = '-';
    } else {
      // ИСПРАВЛЕНИЕ: Рассчитываем с криптосуммой
      try {
        const result = await window.calculateCryptoAmount(coinAmount, this.selectedCurrency, false);
        const afterFee = dollarAmount - data.networkFee;
        const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

        if (result.status === "success" && afterFee > 0) {
          const cryptoAmount = parseFloat(result.raw_amount || result.amount);
          const currencyPrefix = this.getCurrencyPrefix();

          // ИСПРАВЛЕНИЕ: Формат $X.XX (CURRENCY 0.00000000)
          requirementValues[3].textContent = `$${afterFee.toFixed(2)} (${currencyPrefix} ${cryptoAmount.toFixed(8)})`;
          requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;

          console.log(`[Calculator] Обновлено "Вы получите": $${afterFee.toFixed(2)} (${currencyPrefix} ${cryptoAmount.toFixed(8)})`);
        } else {
          requirementValues[3].textContent = afterFee > 0 ? `$${afterFee.toFixed(2)}` : 'Комиссия превышает сумму';
          requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;
        }
      } catch (error) {
        console.error('[Calculator] Ошибка расчета криптосуммы:', error);
        const afterFee = dollarAmount - data.networkFee;
        const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

        requirementValues[3].textContent = afterFee > 0 ? `$${afterFee.toFixed(2)}` : 'Комиссия превышает сумму';
        requirementValues[4].textContent = `${efficiency.toFixed(1)}%`;
      }
    }
  }

  /**
   * Управляет отображением области "Вы получите" в зависимости от условий
   */
  updateResultSectionVisibility(coinAmount, hasMinimumAmount, hasSufficientBalance) {
    const resultSection = document.querySelector('.result-section');
    const mainInfoSection = document.querySelector('.main-info-section');

    // Скрываем области, если:
    // 1. Сумма равна 0
    // 2. Недостаточно средств для минимального вывода
    // 3. Недостаточно средств на балансе
    const shouldHide = coinAmount === 0 || !hasMinimumAmount || !hasSufficientBalance;

    // Скрываем/показываем область "Вы получите"
    if (resultSection) {
      if (shouldHide) {
        resultSection.style.display = 'none';
      } else {
        resultSection.style.display = 'block';
      }
    }

    // Скрываем/показываем основную информацию (Минимум к выводу, Сумма к выводу, Сетевая комиссия)
    if (mainInfoSection) {
      if (shouldHide) {
        mainInfoSection.style.display = 'none';
      } else {
        mainInfoSection.style.display = 'block';
      }
    }

    console.log(`[Calculator] Sections visibility: ${shouldHide ? 'hidden' : 'visible'} (amount: ${coinAmount}, hasMin: ${hasMinimumAmount}, hasBalance: ${hasSufficientBalance})`);
  }

  /**
   * ИСПРАВЛЕНИЕ: Получает префикс валюты
   */
  getCurrencyPrefix() {
    const prefixes = {
      eth: 'ETH',
      btc: 'BTC',
      ton: 'TON',
      usdttrc20: 'USDT',
      trx: 'TRX'
    };
    return prefixes[this.selectedCurrency] || this.selectedCurrency.toUpperCase();
  }

  /**
   * Обновляет статус действия (из оригинала)
   */
  updateActionStatus(coinAmount, data) {
    const statusElement = document.getElementById('action-status');
    if (!statusElement) return;

    const userBalance = window.balanceManager ? window.balanceManager.getBalance() : 0;

    if (coinAmount === 0) {
      const enterAmountText = window.appLocalization ?
        window.appLocalization.get('earnings.enter_amount_for_calculation') :
        'Введите сумму для расчета';
      statusElement.textContent = enterAmountText;
      statusElement.className = 'action-status-card neutral';
    } else if (coinAmount > userBalance) {
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_balance') :
        '❌ Недостаточно средств';
      statusElement.textContent = insufficientText;
      statusElement.className = 'action-status-card insufficient';
    } else if (coinAmount < data.minCoins) {
      const belowMinimumText = window.appLocalization ?
        window.appLocalization.get('earnings.below_minimum_amount') :
        '⚠️ Меньше минимальной суммы';
      statusElement.textContent = belowMinimumText;
      statusElement.className = 'action-status-card warning';
    } else {
      const availableText = window.appLocalization ?
        window.appLocalization.get('earnings.available_for_withdrawal_status') :
        '✅ Доступно для вывода';
      statusElement.textContent = availableText;
      statusElement.className = 'action-status-card available';
      statusElement.onclick = () => {
        window.withdrawalTabsManager.switchToTab('withdrawal');
        smoothScrollTo('withdrawal-section');
      };
    }
  }

  /**
   * Проверяет баланс пользователя (из оригинала)
   */
  updateBalanceCheck(amount) {
    const userBalance = window.balanceManager ? window.balanceManager.getBalance() : 0;

    // Обновляем статус табов валют
    this.updateCurrencyTabsStatus(amount);

    // Обновляем индикатор баланса
    if (this.elements.balanceCheck) {
      if (amount === 0) {
        const enterAmountText = window.appLocalization ?
          window.appLocalization.get('earnings.enter_amount_for_calculation') :
          'Введите сумму';
        this.elements.balanceCheck.textContent = enterAmountText;
        this.elements.balanceCheck.className = 'balance-status';
      } else if (amount > userBalance) {
        const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        const insufficientText = window.appLocalization ?
          window.appLocalization.get('earnings.insufficient_available_balance', { balance: userBalance }) :
          `Недостаточно. Доступно к выводу: ${userBalance} ${coinsText}`;
        this.elements.balanceCheck.textContent = insufficientText;
        this.elements.balanceCheck.className = 'balance-status insufficient';
        console.log(`[Calculator] Недостаточно средств: нужно ${amount}, есть ${userBalance}`);
      } else {
        const sufficientText = window.appLocalization ?
          window.appLocalization.get('earnings.sufficient_balance') :
          '✅ Достаточно средств';
        this.elements.balanceCheck.textContent = sufficientText;
        this.elements.balanceCheck.className = 'balance-status sufficient';
      }
    }
  }

  /**
   * Обновляет статус табов валют (из оригинала)
   */
  updateCurrencyTabsStatus(amount) {
    this.elements.currencyTabs.forEach(tab => {
      const currency = tab.dataset.currency;
      const data = this.currencyData[currency];

      if (data && amount > 0) {
        // Проверяем минимальную сумму
        if (amount < data.minCoins) {
          tab.classList.add('insufficient');
        } else {
          tab.classList.remove('insufficient');
        }
      } else {
        tab.classList.remove('insufficient');
      }
    });
  }

  /**
   * Обновляет информацию о валюте (из оригинала)
   */
  updateCurrencyInfo(currency) {
    const data = this.currencyData[currency];
    if (!data) {
      console.warn(`[Calculator] Данные для валюты ${currency} не найдены`);
      return;
    }

    console.log(`[Calculator] Обновление информации о валюте: ${currency}`);

    // ИСПРАВЛЕНИЕ: Обновляем карточку валюты
    this.updateCurrencyCard(currency);

    // Обновляем минимальные суммы и комиссии
    this.updateMinimumDisplay();
    this.updateFeeDisplay();
  }

  /**
   * Обновляет карточку валюты (из оригинала)
   */
  updateCurrencyCard(currency) {
    const data = this.currencyData[currency];
    if (!data) return;

    console.log(`[Calculator] Обновление карточки валюты: ${currency}`);

    // Обновляем название валюты
    if (this.elements.currencyCardName) {
      this.elements.currencyCardName.textContent = data.name;
      console.log(`[Calculator] Название обновлено: ${data.name}`);
    }

    // Обновляем иконку валюты
    this.updateCurrencyCardIcon(currency);

    // Обновляем статус валюты (лучший выбор, хороший, дорогой)
    this.updateCurrencyStatus(currency);

    // Если есть введенная сумма, обновляем расчеты
    if (this.elements.calcAmountInput && this.elements.calcAmountInput.value) {
      const amount = parseInt(this.elements.calcAmountInput.value) || 0;
      this.updateCurrencyCardElements(currency, amount, amount * 0.001);
    } else {
      // Показываем базовую информацию без расчетов
      this.updateCurrencyCardElements(currency, 0, 0);
    }
  }

  /**
   * Обновляет статус валюты (из оригинала)
   */
  updateCurrencyStatus(currency) {
    const statusElement = document.querySelector('.currency-badge');
    if (!statusElement) return;

    const data = this.currencyData[currency];
    if (!data) return;

    // Определяем статус на основе данных валюты
    let statusText = '';
    let statusClass = '';

    if (data.status === 'best') {
      statusText = window.appLocalization ? window.appLocalization.get('currency_status.best') : 'Лучший выбор';
      statusClass = 'status-best';
    } else if (data.status === 'good') {
      statusText = window.appLocalization ? window.appLocalization.get('currency_status.good') : 'Хороший выбор';
      statusClass = 'status-good';
    } else if (data.status === 'expensive') {
      statusText = window.appLocalization ? window.appLocalization.get('currency_status.expensive') : 'Дорогой';
      statusClass = 'status-expensive';
    } else {
      statusText = window.appLocalization ? window.appLocalization.get('currency_status.available') : 'Доступно';
      statusClass = 'status-available';
    }

    statusElement.textContent = statusText;
    statusElement.className = `currency-badge ${statusClass}`;

    console.log(`[Calculator] Статус валюты ${currency}: ${statusText}`);
  }

  /**
   * Обновляет иконку валюты (из оригинала)
   */
  updateCurrencyCardIcon(currency) {
    // ИСПРАВЛЕНИЕ: Обновляем SVG иконку в заголовке карточки валюты
    const currencyCardIcon = document.getElementById('currency-card-icon');
    if (currencyCardIcon) {
      // Очищаем старое содержимое
      currencyCardIcon.innerHTML = '';

      // Создаем новую SVG иконку в зависимости от валюты
      const svgContent = this.getCurrencySVGContent(currency);
      currencyCardIcon.innerHTML = svgContent;

      console.log(`[Calculator] Иконка в заголовке обновлена для ${currency}`);
    }

    // ИСПРАВЛЕНИЕ: Также обновляем старую текстовую иконку если она есть
    const currencyIcon = document.getElementById('currency-icon');
    if (currencyIcon) {
      const iconMap = {
        'ton': '💎',
        'eth': 'Ξ',
        'btc': '₿',
        'usdttrc20': '💵',
        'trx': '🔥'
      };
      currencyIcon.textContent = iconMap[currency] || '💰';
    }
  }

  /**
   * НОВЫЙ МЕТОД: Получает SVG содержимое для валюты
   */
  getCurrencySVGContent(currency) {
    const svgMap = {
      'ton': `
        <!-- TON icon -->
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor"/>
        <path d="M8 8l8 8M16 8l-8 8" fill="none" stroke="currentColor"/>
        <circle cx="12" cy="12" r="3" fill="rgba(255, 255, 255, 0.3)"/>
        <path d="M12 6v2M12 16v2" fill="none" stroke="currentColor"/>
      `,
      'eth': `
        <!-- ETH icon -->
        <path d="M12 2L6 12l6 4 6-4L12 2z" fill="none" stroke="currentColor"/>
        <path d="M6 12l6 6 6-6" fill="none" stroke="currentColor"/>
        <path d="M12 8v8" fill="none" stroke="currentColor"/>
      `,
      'btc': `
        <!-- BTC icon -->
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor"/>
        <path d="M8 12h8M8 8h6a2 2 0 0 1 0 4M8 16h6a2 2 0 0 0 0-4" fill="none" stroke="currentColor"/>
        <path d="M12 6v2M12 16v2" fill="none" stroke="currentColor"/>
      `,
      'usdttrc20': `
        <!-- USDT icon -->
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor"/>
        <path d="M8 9h8M10 6h4v6M12 12v6" fill="none" stroke="currentColor"/>
        <path d="M9 15h6" fill="none" stroke="currentColor"/>
      `,
      'trx': `
        <!-- TRX icon -->
        <path d="M12 2l8 6-8 6-8-6 8-6z" fill="none" stroke="currentColor"/>
        <path d="M4 8l8 6 8-6" fill="none" stroke="currentColor"/>
        <path d="M12 14v8" fill="none" stroke="currentColor"/>
      `
    };

    return svgMap[currency] || `
      <!-- Default crypto icon -->
      <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor"/>
      <path d="M8 12h8M12 8v8" fill="none" stroke="currentColor"/>
    `;
  }

  /**
   * Обновляет отображение комиссий (из оригинала)
   */
  updateFeeDisplay() {
    const currency = this.selectedCurrency;
    const data = this.currencyData[currency];

    if (data) {
      console.log(`[Calculator] Комиссия для ${currency}: $${data.networkFee}`);
      // Здесь можно обновить элементы интерфейса с информацией о комиссиях
    }
  }

  /**
   * Обновляет информацию о минимальной сумме в карточке валюты
   */
  updateMinimumWarning(coinAmount, currency) {
    if (!this.elements.warningSection) return;

    const data = this.currencyData[currency];
    if (!data) {
      this.elements.warningSection.style.display = 'none';
      return;
    }

    const minCoins = data.minCoins;

    // Показываем секцию предупреждения только если введена сумма и она меньше минимума
    if (coinAmount > 0 && coinAmount < minCoins) {
      const minUsd = (minCoins * 0.001).toFixed(2);
      const missingCoins = minCoins - coinAmount;
      const missingUsd = (missingCoins * 0.001).toFixed(2);

      // Обновляем текст с переносами строк
      if (this.elements.minimumRequiredInfo) {
        const coinsText = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        this.elements.minimumRequiredInfo.innerHTML = `${minCoins.toLocaleString()} ${coinsText}<br>($${minUsd})`;
      }

      if (this.elements.amountMissingInfo) {
        const coinsText2 = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        this.elements.amountMissingInfo.innerHTML = `${missingCoins.toLocaleString()} ${coinsText2}<br>($${missingUsd})`;
      }

      // Показываем всю секцию предупреждения
      this.elements.warningSection.style.display = 'block';
    } else {
      // Скрываем секцию предупреждения
      this.elements.warningSection.style.display = 'none';
    }
  }

  /**
   * Обновляет отображение минимальных сумм (из оригинала)
   */
  updateMinimumDisplay() {
    const currency = this.selectedCurrency;
    const data = this.currencyData[currency];

    if (data) {
      console.log(`[Calculator] Минимум для ${currency}: ${data.minCoins} монет`);
      // Здесь можно обновить элементы интерфейса с информацией о минимумах
    }
  }

  /**
   * Форматирует валюту (из оригинала)
   */
  formatCurrency(value) {
    return parseFloat(value).toFixed(2);
  }

  /**
   * Обновляет карточку валюты с расчетами (из оригинала)
   */
  async updateCurrencyCardWithCalculation(currency, coinAmount, dollarAmount) {
    try {
      // Используем серверный расчет если доступен
      if (window.calculateCryptoAmount) {
        const result = await window.calculateCryptoAmount(coinAmount, currency, false);

        if (result.status === "success") {
          console.log(`[Calculator] Расчет для ${currency}: ${coinAmount} монет = ${result.amount} ${currency.toUpperCase()}`);
          // Здесь можно обновить элементы интерфейса с результатами расчета
        }
      }
    } catch (error) {
      console.warn('[Calculator] Ошибка расчета:', error);
    }
  }

  // Методы для обратной совместимости
  onBalanceChange() {
    this.updateBalanceDisplay();
  }

  updateDisplay(amount) {
    this.updateCalculatorDisplay(amount);
  }

  updateCurrencyInfoCard(currencyInfo, amount, dollarAmount) {
    // Заглушка для совместимости
  }

  updateCalculatorResult(currencyInfo, amount, dollarAmount) {
    // Заглушка для совместимости
  }

  /**
   * ИСПРАВЛЕНИЕ: Обработчик обновления данных валют
   */
  onCurrencyDataUpdate(newCurrencyData) {
    console.log('[Calculator] Получены обновленные данные валют:', newCurrencyData);

    // Обновляем локальные данные
    this.currencyData = newCurrencyData;

    // Обновляем отображение если есть введенная сумма
    if (this.elements.calcAmountInput && this.elements.calcAmountInput.value) {
      const amount = parseInt(this.elements.calcAmountInput.value) || 0;
      this.updateCalculatorDisplay(amount);
    }

    // Обновляем информацию о текущей валюте
    if (this.selectedCurrency) {
      this.updateCurrencyInfo(this.selectedCurrency);
    }
  }
}

/**
 * Функция для расчета криптовалюты (из оригинала)
 */
async function calculateCryptoAmount(coinAmount, currency, walletAddress = null, checkBalance = true) {
  if (!coinAmount || coinAmount <= 0) {
    return { amount: "0", status: "empty", message: "" };
  }

  // Проверяем баланс пользователя
  const userBalance = window.balanceManager ? window.balanceManager.getBalance() : 0;
  if (checkBalance && coinAmount > userBalance) {
    const needed = coinAmount - userBalance;
    return {
      amount: "0",
      status: "insufficient_balance",
      message: `Недостаточно средств! Нужно: ${needed} монет`
    };
  }

  try {
    // Пробуем серверный расчет
    const response = await fetch(`${window.API_BASE_URL}/calculateCrypto.php`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        coins_amount: coinAmount,
        currency: currency,
        check_balance: checkBalance
      })
    });

    if (response.ok) {
      const data = await response.json();

      if (data.error) {
        console.warn(`[calculateCryptoAmount] Ошибка API: ${data.error}. Код: ${data.error_code || 'N/A'}`);
        // Если API вернул конкретную ошибку, возвращаем ее напрямую
        if (data.error_code) {
          return {
            amount: "0", status: data.error_code, message: data.error, raw_amount: 0
          };
        }
        return calculateCryptoAmountFallback(coinAmount, currency);
      }

      // Все криптовалюты округляем до 8 знаков после запятой для точности
      const cryptoAmount = parseFloat(data.crypto_amount);
      const formattedAmount = cryptoAmount.toFixed(8);

      console.log(`Серверный расчет для ${currency}: ${coinAmount} монет = ${formattedAmount} ${currency.toUpperCase()}`);

      return {
        amount: formattedAmount,
        status: "success",
        message: "",
        raw_amount: cryptoAmount,
        usd_amount: data.usd_amount,
        fee_details: data.fee_details,
        calculation_method: data.calculation_method || 'unified_fee_calculator'
      };
    }
  } catch (error) {
    console.warn(`Ошибка серверного расчета для ${currency}:`, error);
    // Fallback if network error or JSON parsing error
  }

  // Fallback на локальный расчет
  return calculateCryptoAmountFallback(coinAmount, currency);
}

/**
 * Fallback функция для локального расчета (из оригинала)
 */
function calculateCryptoAmountFallback(coinAmount, currency) {
  console.warn(`Используем fallback расчет для ${currency} (С ВЫЧЕТОМ комиссии - показываем что получит пользователь)`);

  const coinValue = window.appSettings ? window.appSettings.getCoinValue() : 0.001;
  const dollarAmount = coinAmount * coinValue;

  // ОБНОВЛЕННЫЕ курсы (синхронизированы с сервером)
  const fallbackRates = {
    'eth': 2502,      // ETH ~$2502
    'btc': 104620,    // BTC ~$104620
    'ton': 2.9,       // TON ~$2.9
    'usdttrc20': 1.0, // USDT ~$1.0
    'trx': 0.25       // TRX ~$0.25
  };

  // Fallback комиссии (сетевые комиссии)
  const fallbackFees = {
    'eth': 0.002,      // 0.002 ETH
    'btc': 0.0001,     // 0.0001 BTC
    'ton': 0.05,       // 0.05 TON
    'usdttrc20': 1.5,  // 1.5 USDT
    'trx': 1.0         // 1.0 TRX
  };

  const rate = fallbackRates[currency] || 1;
  const fee = fallbackFees[currency] || 0;

  // Рассчитываем сумму ДО вычета комиссии
  const grossCryptoAmount = dollarAmount / rate;

  // Рассчитываем сумму ПОСЛЕ вычета комиссии (то что получит пользователь)
  const netCryptoAmount = Math.max(0, grossCryptoAmount - fee);

  const formattedAmount = netCryptoAmount.toFixed(8);

  console.log(`Fallback расчет для ${currency}: ${coinAmount} монет = ${formattedAmount} ${currency.toUpperCase()} (после вычета комиссии ${fee})`);

  return {
    amount: formattedAmount,
    status: "success",
    message: "",
    raw_amount: netCryptoAmount,
    gross_amount: grossCryptoAmount, // Сумма до вычета комиссии (для отправки в NOWPayments)
    usd_amount: dollarAmount,
    fee_amount: fee,
    calculation_method: 'fallback_with_fee'
  };
}

window.calculatorManager = new OriginalCalculatorManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.calculateCryptoAmount = calculateCryptoAmount;
window.calculateCryptoAmountFallback = calculateCryptoAmountFallback;
window.initializeWithdrawalCalculator = () => window.calculatorManager.init();
window.updateCalculatorDisplay = (amount) => window.calculatorManager.updateCalculatorDisplay(amount);

console.log('🧮 [CalculatorManager] Менеджер калькулятора загружен с полной интеграцией.');