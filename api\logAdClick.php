<?php
/**
 * api/logAdClick.php
 * Логирование кликов по рекламным кнопкам (даже если реклама не показалась)
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Метод не поддерживается']);
    exit;
}

// ТЕСТ: Немедленный ответ для проверки, что мы в правильном файле
echo json_encode(['test' => 'logAdClick.php работает!', 'timestamp' => time()]);
exit;

// Подключаем зависимости
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/validate_initdata.php';
require_once __DIR__ . '/recordAdView.php';

// ТЕСТ: Проверяем, что скрипт вообще выполняется
error_log("=== ТЕСТ: logAdClick.php ЗАПУЩЕН ===");

try {
    // ТЕСТ: Проверяем, что логирование работает
    error_log("=== НАЧАЛО logAdClick API ===");

    // Получение IP и User Agent
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // Получение и декодирование входных данных
    $inputJSON = file_get_contents('php://input');
    error_log("DEBUG logAdClick: Получен JSON: " . $inputJSON);
    $input = json_decode($inputJSON, true);
    error_log("DEBUG logAdClick: Декодированные данные: " . print_r($input, true));

    if ($input === null) {
        throw new Exception('Неверный JSON');
    }

    // Валидация обязательных полей
    if (!isset($input['initData']) || empty($input['initData'])) {
        throw new Exception('Отсутствуют данные пользователя');
    }

    if (!isset($input['adType']) || empty($input['adType'])) {
        throw new Exception('Не указан тип рекламы');
    }

    if (!isset($input['clickType']) || empty($input['clickType'])) {
        throw new Exception('Не указан тип клика');
    }

    $initData = $input['initData'];
    $adType = $input['adType'];
    $clickType = $input['clickType']; // 'button_click', 'limit_reached', 'cooldown_active', etc.

    // Валидация initData с fallback механизмом
    error_log("logAdClick DEBUG: Начинаем валидацию initData: " . substr($initData, 0, 100) . "...");
    $validatedData = validateTelegramInitData($initData);
    error_log("logAdClick DEBUG: Результат validateTelegramInitData: " . ($validatedData === false ? 'FALSE' : 'SUCCESS'));

    if ($validatedData === false) {
        error_log("logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную");

        // Fallback: пытаемся извлечь данные пользователя напрямую
        $initDataParts = [];
        parse_str($initData, $initDataParts);

        if (isset($initDataParts['user'])) {
            $userArray = json_decode($initDataParts['user'], true);
            if ($userArray !== null && isset($userArray['id'])) {
                $validatedData = ['user' => $userArray];
                error_log("logAdClick INFO: Упрощенная валидация прошла для пользователя " . $userArray['id']);
            } else {
                error_log("logAdClick ERROR: Не удалось извлечь данные пользователя из initData");
                throw new Exception('Неверные данные пользователя');
            }
        } else {
            error_log("logAdClick ERROR: Отсутствуют данные пользователя в initData");
            throw new Exception('Нет данных пользователя в initData');
        }
    }

    $userId = intval($validatedData['user']['id']);
    error_log("logAdClick INFO: initData валидирован для пользователя {$userId}");

    // Дополнительные данные для логирования
    $additionalData = [
        'click_type' => $clickType,
        'timestamp_ms' => $input['timestamp'] ?? time() * 1000,
        'session_id' => $input['sessionId'] ?? 'unknown'
    ];

    // Добавляем причину если есть
    if (isset($input['reason']) && !empty($input['reason'])) {
        $additionalData['reason'] = $input['reason'];
    }

    // Добавляем информацию о лимитах если есть
    if (isset($input['limitInfo']) && !empty($input['limitInfo'])) {
        $additionalData['limit_info'] = $input['limitInfo'];
    }

    // Логируем клик
    logAdRequest($userId, $adType, $clickType, $clientIp, $userAgent, '', $additionalData);

    // Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'message' => 'Клик зарегистрирован',
        'user_id' => $userId,
        'ad_type' => $adType,
        'click_type' => $clickType,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log("logAdClick ERROR: " . $e->getMessage());
    
    // Логируем ошибочный запрос
    $userId = 0;
    $adType = $input['adType'] ?? 'unknown';
    $clickType = 'error';
    
    logAdRequest($userId, $adType, $clickType, $clientIp, $userAgent, '', [
        'error_message' => $e->getMessage(),
        'input_data' => $input ?? []
    ]);

    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage(),
        'success' => false
    ]);
}
?>
