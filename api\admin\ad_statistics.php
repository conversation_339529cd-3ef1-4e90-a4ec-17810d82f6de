<?php
/**
 * api/admin/ad_statistics.php
 * Страница статистики рекламных показов
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключаем шаблон заголовка
$pageTitle = 'Статистика рекламы';
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">📊 Статистика рекламных показов</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportStats()">
                            <i class="bi bi-download"></i> Экспорт
                        </button>
                    </div>
                </div>
            </div>

            <!-- Фильтры -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🔍 Фильтры</h5>
                </div>
                <div class="card-body">
                    <form id="filtersForm" class="row g-3">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label">Дата с:</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label">Дата по:</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to">
                        </div>
                        <div class="col-md-2">
                            <label for="adType" class="form-label">Тип рекламы:</label>
                            <select class="form-select" id="adType" name="ad_type">
                                <option value="">Все типы</option>
                                <option value="native_banner">Баннер</option>
                                <option value="interstitial">Интерстициальная</option>
                                <option value="rewarded_video">Видео</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Статус:</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Все статусы</option>
                                <option value="button_click">Клик по кнопке</option>
                                <option value="ad_request">Запрос рекламы</option>
                                <option value="success">Успешно</option>
                                <option value="no_ads_available">Нет рекламы</option>
                                <option value="empty">Пустой</option>
                                <option value="error">Ошибка</option>
                                <option value="ad_error">Ошибка показа</option>
                                <option value="limit_reached">Лимит достигнут</option>
                                <option value="limit_exceeded">Лимит превышен</option>
                                <option value="ad_already_showing">Реклама показывается</option>
                                <option value="cooldown_active">Активен кулдаун</option>
                                <option value="ads_unavailable">Реклама недоступна</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="country" class="form-label">Страна:</label>
                            <select class="form-select" id="country" name="country">
                                <option value="">Все страны</option>
                                <option value="RU">Россия</option>
                                <option value="Unknown">Неизвестно</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> Применить фильтры
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                <i class="bi bi-x-circle"></i> Сбросить
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Загрузка -->
            <div id="loadingIndicator" class="text-center mb-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Загрузка...</span>
                </div>
                <p class="mt-2">Загрузка статистики...</p>
            </div>

            <!-- Общая статистика -->
            <div id="generalStats" class="row mb-4">
                <!-- Карточки статистики будут добавлены через JavaScript -->
            </div>

            <!-- Графики -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">📈 Статистика по дням</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">🕐 Статистика по часам</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="hourlyChart" width="100%" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Детальная статистика -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">🎯 По типам рекламы</h6>
                        </div>
                        <div class="card-body">
                            <div id="adTypeStats">
                                <!-- Данные будут загружены через JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">🌍 Топ стран</h6>
                        </div>
                        <div class="card-body">
                            <div id="countryStats">
                                <!-- Данные будут загружены через JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">👥 Топ пользователей</h6>
                        </div>
                        <div class="card-body">
                            <div id="userStats">
                                <!-- Данные будут загружены через JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let dailyChart = null;
let hourlyChart = null;

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    // Устанавливаем дату по умолчанию (последние 7 дней)
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('dateFrom').value = weekAgo.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    
    // Загружаем статистику
    loadStatistics();
});

// Загрузка статистики
async function loadStatistics() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = 'block';
    
    try {
        const formData = new FormData(document.getElementById('filtersForm'));
        const params = new URLSearchParams(formData);
        
        const response = await fetch(`ad_stats_api.php?${params}`);
        const result = await response.json();
        
        if (result.success) {
            displayStatistics(result.data);
        } else {
            alert('Ошибка загрузки статистики: ' + result.error);
        }
    } catch (error) {
        console.error('Ошибка:', error);
        alert('Ошибка загрузки данных');
    } finally {
        loadingIndicator.style.display = 'none';
    }
}

// Отображение статистики
function displayStatistics(data) {
    displayGeneralStats(data);
    displayCharts(data);
    displayDetailedStats(data);
}

// Отображение общей статистики
function displayGeneralStats(data) {
    const container = document.getElementById('generalStats');

    const stats = [
        {
            title: 'Всего запросов',
            value: data.total_requests,
            icon: '📊',
            color: 'primary'
        },
        {
            title: 'Клики кнопок',
            value: data.button_clicks || 0,
            icon: '👆',
            color: 'info'
        },
        {
            title: 'Запросы рекламы',
            value: data.ad_requests || 0,
            icon: '🎯',
            color: 'secondary'
        },
        {
            title: 'Успешных показов',
            value: data.successful_shows,
            icon: '✅',
            color: 'success'
        },
        {
            title: 'Нет рекламы',
            value: data.no_ads_available || 0,
            icon: '🚫',
            color: 'warning'
        },
        {
            title: 'Ошибок',
            value: data.error_requests,
            icon: '❌',
            color: 'danger'
        }
    ];

    container.innerHTML = stats.map(stat => `
        <div class="col-md-2 mb-3">
            <div class="card border-left-${stat.color} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-${stat.color} text-uppercase mb-1">
                                ${stat.title}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${stat.icon} ${stat.value}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Отображение графиков
function displayCharts(data) {
    // График по дням
    const dailyCtx = document.getElementById('dailyChart').getContext('2d');
    if (dailyChart) {
        dailyChart.destroy();
    }

    const dailyLabels = Object.keys(data.by_date);
    const dailyTotal = dailyLabels.map(date => data.by_date[date].total);
    const dailySuccess = dailyLabels.map(date => data.by_date[date].success);

    dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: dailyLabels,
            datasets: [{
                label: 'Всего запросов',
                data: dailyTotal,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: 'Успешных показов',
                data: dailySuccess,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // График по часам
    const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    if (hourlyChart) {
        hourlyChart.destroy();
    }

    const hourlyLabels = [];
    const hourlyData = [];
    for (let i = 0; i < 24; i++) {
        const hour = i.toString().padStart(2, '0');
        hourlyLabels.push(hour + ':00');
        hourlyData.push(data.by_hour[hour] || 0);
    }

    hourlyChart = new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: hourlyLabels,
            datasets: [{
                label: 'Запросов по часам',
                data: hourlyData,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Отображение детальной статистики
function displayDetailedStats(data) {
    // Статистика по типам рекламы
    const adTypeContainer = document.getElementById('adTypeStats');
    let adTypeHtml = '';

    for (const [type, stats] of Object.entries(data.by_ad_type)) {
        const successRate = stats.total > 0 ? ((stats.success / stats.total) * 100).toFixed(1) : 0;
        adTypeHtml += `
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span class="fw-bold">${getAdTypeName(type)}</span>
                    <span class="badge bg-primary">${stats.total}</span>
                </div>
                <div class="progress mb-1" style="height: 6px;">
                    <div class="progress-bar bg-success" style="width: ${successRate}%"></div>
                </div>
                <small class="text-muted">
                    Успешно: ${stats.success} (${successRate}%) |
                    Пустых: ${stats.empty} |
                    Ошибок: ${stats.error}
                </small>
            </div>
        `;
    }
    adTypeContainer.innerHTML = adTypeHtml;

    // Статистика по странам
    const countryContainer = document.getElementById('countryStats');
    let countryHtml = '';

    for (const [country, count] of Object.entries(data.top_countries)) {
        const percentage = data.total_requests > 0 ? ((count / data.total_requests) * 100).toFixed(1) : 0;
        countryHtml += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${getCountryName(country)}</span>
                <div>
                    <span class="badge bg-info me-1">${count}</span>
                    <small class="text-muted">${percentage}%</small>
                </div>
            </div>
        `;
    }
    countryContainer.innerHTML = countryHtml;

    // Статистика по пользователям
    const userContainer = document.getElementById('userStats');
    let userHtml = '';

    for (const [userId, stats] of Object.entries(data.top_users)) {
        const successRate = stats.total > 0 ? ((stats.success / stats.total) * 100).toFixed(1) : 0;
        userHtml += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-truncate" style="max-width: 120px;">
                    <small>ID: ${userId}</small>
                </span>
                <div class="text-end">
                    <div class="badge bg-success">${stats.success}</div>
                    <div><small class="text-muted">${successRate}% из ${stats.total}</small></div>
                </div>
            </div>
        `;
    }
    userContainer.innerHTML = userHtml;
}

// Вспомогательные функции
function getAdTypeName(type) {
    const names = {
        'native_banner': '🎯 Нативный баннер',
        'interstitial': '📱 Интерстициальная',
        'rewarded_video': '🎬 Видео с наградой',
        'interstitial_banner_view': '📱 Интерстициальный баннер',
        'default': '📊 Обычная реклама'
    };
    return names[type] || names['default'];
}

function getCountryName(code) {
    const countries = {
        'RU': '🇷🇺 Россия',
        'US': '🇺🇸 США',
        'Unknown': '❓ Неизвестно'
    };
    return countries[code] || `🌍 ${code}`;
}

// Применение фильтров
function applyFilters() {
    loadStatistics();
}

// Сброс фильтров
function resetFilters() {
    document.getElementById('filtersForm').reset();

    // Устанавливаем дату по умолчанию (последние 7 дней)
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    document.getElementById('dateFrom').value = weekAgo.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];

    loadStatistics();
}

// Обновление статистики
function refreshStats() {
    loadStatistics();
}

// Экспорт статистики
async function exportStats() {
    try {
        const formData = new FormData(document.getElementById('filtersForm'));
        const params = new URLSearchParams(formData);

        const response = await fetch(`ad_stats_api.php?${params}&export=1`);
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'ad_statistics_' + new Date().toISOString().split('T')[0] + '.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('Ошибка экспорта:', error);
        alert('Ошибка экспорта данных');
    }
}
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
