<?php
/**
 * api/admin/security.php
 * Страница безопасности
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить security.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $userId = intval($_POST['user_id']);
    
    switch ($_POST['action']) {
        case 'unblock_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = false;
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно разблокирован";
                    
                    // Логируем разблокировку пользователя
                    logAuditEvent('admin_unblock_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'block_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = true;
                $userData[$userId]['blocked_at'] = time();
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно заблокирован";
                    
                    // Логируем блокировку пользователя
                    logAuditEvent('admin_block_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'reset_suspicious':
            if (isset($userData[$userId])) {
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Счетчик подозрительной активности пользователя $userId успешно сброшен";
                    
                    // Логируем сброс счетчика подозрительной активности
                    logAuditEvent('admin_reset_suspicious', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
    }
}

// Получение журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_slice(array_reverse(file($auditLogFile)), 0, 100);
}

// Фильтрация пользователей с подозрительной активностью
$suspiciousUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers[$userId] = $user;
    }
}

// Сортировка пользователей с подозрительной активностью по убыванию счетчика
uasort($suspiciousUsers, function($a, $b) {
    return ($b['suspicious_activity'] ?? 0) - ($a['suspicious_activity'] ?? 0);
});

// Фильтрация заблокированных пользователей
$blockedUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers[$userId] = $user;
    }
}

// Сортировка заблокированных пользователей по дате блокировки
uasort($blockedUsers, function($a, $b) {
    return ($b['blocked_at'] ?? 0) - ($a['blocked_at'] ?? 0);
});

// Обработка POST запроса для переключения лимитов
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_limits'])) {
    $settingsFile = __DIR__ . '/ad_limits_settings.json';

    try {
        // Загружаем текущие настройки
        if (!file_exists($settingsFile)) {
            $settings = [
                'disable_all_ad_limits' => false,
                'last_updated' => null,
                'updated_by' => null
            ];
        } else {
            $content = file_get_contents($settingsFile);
            $settings = json_decode($content, true);
            if (!$settings) {
                $settings = [
                    'disable_all_ad_limits' => false,
                    'last_updated' => null,
                    'updated_by' => null
                ];
            }
        }

        // Переключаем состояние
        $oldValue = $settings['disable_all_ad_limits'];
        $newValue = !$oldValue;

        $settings['disable_all_ad_limits'] = $newValue;
        $settings['last_updated'] = date('Y-m-d H:i:s');
        $settings['updated_by'] = $_SESSION['admin_username'] ?? 'admin';

        // Сохраняем настройки
        $result = file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result !== false) {
            $action = $newValue ? 'ОТКЛЮЧЕНЫ' : 'ВКЛЮЧЕНЫ';
            error_log("AD_LIMITS_CONTROL: Лимиты рекламы {$action} администратором через POST");

            $successMessage = $newValue ? 'Все лимиты рекламы отключены!' : 'Лимиты рекламы включены!';
        } else {
            $errorMessage = 'Ошибка сохранения настроек';
        }

    } catch (Exception $e) {
        error_log("security.php: Ошибка переключения лимитов: " . $e->getMessage());
        $errorMessage = 'Ошибка: ' . $e->getMessage();
    }
}

// Получаем текущие настройки лимитов
$limitsSettings = null;
$settingsFile = __DIR__ . '/ad_limits_settings.json';
try {
    if (file_exists($settingsFile)) {
        $content = file_get_contents($settingsFile);
        $limitsSettings = json_decode($content, true);
    }
    if (!$limitsSettings) {
        $limitsSettings = ['disable_all_ad_limits' => false];
    }
} catch (Exception $e) {
    error_log("security.php: Ошибка загрузки настроек лимитов: " . $e->getMessage());
    $limitsSettings = ['disable_all_ad_limits' => false];
}

// Получаем данные о лимитах для встраивания в страницу
try {
    $limitsData = getCurrentLimits();
    $limitsJson = json_encode($limitsData, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("security.php: Ошибка получения лимитов: " . $e->getMessage());
    $limitsData = null;
    $limitsJson = 'null';
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<style>
.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e3e6f0;
}
.log-entry:last-child {
    border-bottom: none;
}
#peak-hours-list .badge {
    margin: 0.1rem;
}
.table th {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
}
.spinner-border {
    width: 2rem;
    height: 2rem;
}
</style>

<div class="container-fluid">
    <div class="row">        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Безопасность</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Вкладки -->
            <ul class="nav nav-tabs mb-4" id="securityTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="suspicious-tab" data-bs-toggle="tab" data-bs-target="#suspicious" type="button" role="tab" aria-controls="suspicious" aria-selected="true">
                        Подозрительная активность <span class="badge bg-warning"><?php echo count($suspiciousUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="blocked-tab" data-bs-toggle="tab" data-bs-target="#blocked" type="button" role="tab" aria-controls="blocked" aria-selected="false">
                        Заблокированные пользователи <span class="badge bg-danger"><?php echo count($blockedUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" type="button" role="tab" aria-controls="audit" aria-selected="false">
                        Журнал аудита
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="limits-tab" data-bs-toggle="tab" data-bs-target="#limits" type="button" role="tab" aria-controls="limits" aria-selected="false">
                        Лимиты системы <span class="badge bg-info" id="peak-time-badge"></span>
                    </button>
                </li>
            </ul>

            <!-- Содержимое вкладок -->
            <div class="tab-content" id="securityTabsContent">
                <!-- Подозрительная активность -->
                <div class="tab-pane fade show active" id="suspicious" role="tabpanel" aria-labelledby="suspicious-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Пользователи с подозрительной активностью</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($suspiciousUsers)): ?>
                                <p class="text-center">Нет пользователей с подозрительной активностью</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Уровень подозрительности</th>
                                                <th>Баланс</th>
                                                <th>Дата регистрации</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($suspiciousUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo min(100, ($user['suspicious_activity'] / SUSPICIOUS_ACTIVITY_THRESHOLD) * 100); ?>%" aria-valuenow="<?php echo $user['suspicious_activity']; ?>" aria-valuemin="0" aria-valuemax="<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>">
                                                                <?php echo $user['suspicious_activity']; ?>/<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?></td>
                                                    <td>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="block_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger">
                                                                <i class="bi bi-lock"></i> Заблокировать
                                                            </button>
                                                        </form>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="reset_suspicious">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-warning">
                                                                <i class="bi bi-arrow-counterclockwise"></i> Сбросить
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Заблокированные пользователи -->
                <div class="tab-pane fade" id="blocked" role="tabpanel" aria-labelledby="blocked-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Заблокированные пользователи</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($blockedUsers)): ?>
                                <p class="text-center">Нет заблокированных пользователей</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Дата блокировки</th>
                                                <th>Баланс</th>
                                                <th>Подозрительная активность</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($blockedUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td><?php echo isset($user['blocked_at']) ? date('Y-m-d H:i', $user['blocked_at']) : 'Неизвестно'; ?></td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo $user['suspicious_activity'] ?? 0; ?></td>
                                                    <td>
                                                        <form method="post">
                                                            <input type="hidden" name="action" value="unblock_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success">
                                                                <i class="bi bi-unlock"></i> Разблокировать
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Журнал аудита -->
                <div class="tab-pane fade" id="audit" role="tabpanel" aria-labelledby="audit-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Журнал аудита (последние 100 записей)</h6>
                            <form method="post">
                                <input type="hidden" name="action" value="clear_audit_log">
                                <button type="submit" class="btn btn-sm btn-outline-danger">Очистить журнал</button>
                            </form>
                        </div>
                        <div class="card-body">
                            <?php if (empty($auditLog)): ?>
                                <p class="text-center">Журнал аудита пуст</p>
                            <?php else: ?>
                                <div style="max-height: 500px; overflow-y: auto;">
                                    <?php foreach ($auditLog as $logEntry): ?>
                                        <div class="log-entry small"><?php echo htmlspecialchars($logEntry); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Лимиты системы -->
                <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Текущие лимиты системы безопасности</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary me-2" onclick="testSession()">
                                    <i class="fas fa-bug"></i> Тест сессии
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshLimits()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Сообщения об успехе/ошибке -->
                            <?php if (isset($successMessage)): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($successMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($errorMessage)): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($errorMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <!-- Управление лимитами -->
                            <div class="alert alert-warning mb-4" id="limits-control-section">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="alert-heading mb-2">
                                            <i class="fas fa-exclamation-triangle"></i> Управление лимитами рекламы
                                        </h5>
                                        <p class="mb-0">
                                            <strong>Внимание:</strong> Отключение лимитов полностью снимает ограничения на просмотры рекламы.
                                            IP-ограничения и блокировка подозрительных пользователей остаются активными.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <form method="POST" style="display: inline;" id="limitsToggleForm">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="disableAllLimits"
                                                       <?php echo $limitsSettings['disable_all_ad_limits'] ? 'checked' : ''; ?>
                                                       onchange="if(confirmToggleLimits(this)) this.form.submit();">
                                                <input type="hidden" name="toggle_limits" value="1">
                                                <label class="form-check-label fw-bold" for="disableAllLimits">
                                                    Отключить все лимиты рекламы
                                                </label>
                                            </div>
                                        </form>
                                        <small class="<?php echo $limitsSettings['disable_all_ad_limits'] ? 'text-danger fw-bold' : 'text-success'; ?>">
                                            <?php echo $limitsSettings['disable_all_ad_limits'] ? 'Все лимиты рекламы отключены' : 'Лимиты рекламы активны'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div id="limits-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка информации о лимитах...</p>
                            </div>

                            <div id="limits-content" style="display: none;">
                                <!-- Статус системы -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card border-left-primary">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                            Статус системы
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="system-status">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-left-info">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                            Время UTC
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="current-time">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Лимиты рекламы по типам -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Лимиты просмотров рекламы по типам</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Просмотров в час (по типу)</strong></td>
                                                        <td><span id="base-type-hour">-</span></td>
                                                        <td><span id="current-type-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Просмотров в день (по типу)</strong></td>
                                                        <td><span id="base-type-day">-</span></td>
                                                        <td><span id="current-type-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-day" class="badge">-</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Общие лимиты -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Общие лимиты системы</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Общих просмотров в час</strong></td>
                                                        <td><span id="base-general-hour">-</span></td>
                                                        <td><span id="current-general-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Общих просмотров в день</strong></td>
                                                        <td><span id="base-general-day">-</span></td>
                                                        <td><span id="current-general-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-day" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Запросов в минуту (IP)</strong></td>
                                                        <td><span id="base-requests-minute">-</span></td>
                                                        <td><span id="current-requests-minute" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Порог подозрительной активности</strong></td>
                                                        <td><span id="base-suspicious">-</span></td>
                                                        <td><span id="current-suspicious" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Пиковые часы -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Настройки пиковых часов</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Пиковые часы (UTC):</strong></p>
                                                <div id="peak-hours-list" class="mb-3">
                                                    Загрузка...
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Множители в пиковое время:</strong></p>
                                                <ul>
                                                    <li>Часовые лимиты: <span class="badge bg-warning">x2.0</span></li>
                                                    <li>Дневные лимиты: <span class="badge bg-info">x1.5</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Встроенные данные о лимитах
const embeddedLimitsData = <?php echo $limitsJson; ?>;

// Встроенные настройки лимитов
const embeddedLimitsSettings = <?php echo json_encode($limitsSettings, JSON_UNESCAPED_UNICODE); ?>;

// Функция для загрузки настроек лимитов (теперь использует встроенные данные)
function loadLimitsSettings() {
    console.log('Используем встроенные настройки лимитов:', embeddedLimitsSettings);

    const checkbox = document.getElementById('disableAllLimits');
    const statusText = document.getElementById('limits-status-text');

    if (checkbox && embeddedLimitsSettings) {
        checkbox.checked = embeddedLimitsSettings.disable_all_ad_limits;
    }

    if (statusText && embeddedLimitsSettings) {
        if (embeddedLimitsSettings.disable_all_ad_limits) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }
}

// Функция для загрузки информации о лимитах
function loadLimitsInfo() {
    document.getElementById('limits-loading').style.display = 'block';
    document.getElementById('limits-content').style.display = 'none';

    // Сначала пробуем использовать встроенные данные
    if (embeddedLimitsData) {
        console.log('Используем встроенные данные о лимитах');
        const mockResponse = {
            success: true,
            timestamp: Math.floor(Date.now() / 1000),
            current_time_utc: new Date().toISOString().slice(0, 19).replace('T', ' '),
            current_time_local: new Date().toLocaleString(),
            limits_info: embeddedLimitsData,
            status: embeddedLimitsData.is_peak_time ? 'PEAK_TIME' : 'NORMAL_TIME',
            multipliers: {
                peak_hourly_multiplier: 2.0,
                peak_daily_multiplier: 1.5
            }
        };

        updateLimitsDisplay(mockResponse);
        document.getElementById('limits-loading').style.display = 'none';
        document.getElementById('limits-content').style.display = 'block';
        return;
    }

    // Если встроенных данных нет, делаем AJAX запрос
    fetch('current_limits.php', {
        method: 'GET',
        credentials: 'same-origin', // Включаем cookies для сессии
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Сессия истекла. Пожалуйста, войдите в систему заново.');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateLimitsDisplay(data);
                document.getElementById('limits-loading').style.display = 'none';
                document.getElementById('limits-content').style.display = 'block';
            } else {
                throw new Error(data.error || 'Ошибка загрузки данных');
            }
        })
        .catch(error => {
            console.error('Ошибка загрузки лимитов:', error);
            document.getElementById('limits-loading').innerHTML =
                '<div class="alert alert-danger"><strong>Ошибка загрузки данных о лимитах:</strong><br>' + error.message +
                '<br><button class="btn btn-sm btn-primary mt-2" onclick="loadLimitsInfo()">Попробовать снова</button>' +
                '<br><button class="btn btn-sm btn-secondary mt-2" onclick="testSession()">Тест сессии</button></div>';
        });
}

// Функция для обновления отображения лимитов
function updateLimitsDisplay(data) {
    const limits = data.limits_info.limits;
    const baseLimits = data.limits_info.base_limits;
    const isPeakTime = data.limits_info.is_peak_time;

    // Используем встроенные настройки для определения состояния лимитов
    const limitsDisabled = embeddedLimitsSettings ? embeddedLimitsSettings.disable_all_ad_limits : false;

    // Обновляем переключатель лимитов (но не меняем его состояние, так как это управляется формой)
    const statusText = document.getElementById('limits-status-text');

    if (statusText) {
        if (limitsDisabled) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }

    // Обновляем статус системы
    if (limitsDisabled) {
        document.getElementById('system-status').textContent = 'ЛИМИТЫ ОТКЛЮЧЕНЫ';
        document.getElementById('system-status').className = 'h5 mb-0 font-weight-bold text-danger';
    } else {
        document.getElementById('system-status').textContent =
            isPeakTime ? 'ПИКОВОЕ ВРЕМЯ' : 'ОБЫЧНОЕ ВРЕМЯ';
        document.getElementById('system-status').className =
            'h5 mb-0 font-weight-bold ' + (isPeakTime ? 'text-warning' : 'text-success');
    }

    // Обновляем время
    document.getElementById('current-time').textContent = data.current_time_utc;

    // Обновляем badge в заголовке вкладки
    const badge = document.getElementById('peak-time-badge');
    if (limitsDisabled) {
        badge.textContent = 'ОТКЛЮЧЕНЫ';
        badge.className = 'badge bg-danger';
    } else if (isPeakTime) {
        badge.textContent = 'ПИКОВОЕ';
        badge.className = 'badge bg-warning';
    } else {
        badge.textContent = 'ОБЫЧНОЕ';
        badge.className = 'badge bg-success';
    }

    // Лимиты по типам рекламы
    document.getElementById('base-type-hour').textContent = baseLimits.ad_views_per_type_per_hour;
    document.getElementById('current-type-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_hour;
    updateMultiplierBadge('multiplier-type-hour', baseLimits.ad_views_per_type_per_hour, limits.ad_views_per_type_per_hour, limitsDisabled);

    document.getElementById('base-type-day').textContent = baseLimits.ad_views_per_type_per_day;
    document.getElementById('current-type-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_day;
    updateMultiplierBadge('multiplier-type-day', baseLimits.ad_views_per_type_per_day, limits.ad_views_per_type_per_day, limitsDisabled);

    // Общие лимиты
    document.getElementById('base-general-hour').textContent = baseLimits.ad_views_per_hour;
    document.getElementById('current-general-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_hour;
    updateMultiplierBadge('multiplier-general-hour', baseLimits.ad_views_per_hour, limits.ad_views_per_hour, limitsDisabled);

    document.getElementById('base-general-day').textContent = baseLimits.ad_views_per_day;
    document.getElementById('current-general-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_day;
    updateMultiplierBadge('multiplier-general-day', baseLimits.ad_views_per_day, limits.ad_views_per_day, limitsDisabled);

    // Статические лимиты
    document.getElementById('base-requests-minute').textContent = limits.requests_per_minute;
    document.getElementById('current-requests-minute').textContent = limits.requests_per_minute;

    document.getElementById('base-suspicious').textContent = limits.suspicious_activity_threshold;
    document.getElementById('current-suspicious').textContent = limits.suspicious_activity_threshold;

    // Пиковые часы
    const peakHours = data.limits_info.peak_hours.map(h => h + ':00').join(', ');
    document.getElementById('peak-hours-list').innerHTML =
        '<span class="badge bg-info me-1">' + peakHours.split(', ').join('</span> <span class="badge bg-info me-1">') + '</span>';
}

// Функция для обновления badge множителя
function updateMultiplierBadge(elementId, baseValue, currentValue, limitsDisabled = false) {
    const element = document.getElementById(elementId);

    if (limitsDisabled) {
        element.textContent = '∞';
        element.className = 'badge bg-danger';
        return;
    }

    const multiplier = currentValue / baseValue;

    if (multiplier === 1) {
        element.textContent = '1x';
        element.className = 'badge bg-secondary';
    } else if (multiplier === 1.5) {
        element.textContent = '1.5x';
        element.className = 'badge bg-info';
    } else if (multiplier === 2) {
        element.textContent = '2x';
        element.className = 'badge bg-warning';
    } else {
        element.textContent = multiplier.toFixed(1) + 'x';
        element.className = 'badge bg-primary';
    }
}

// Функция для обновления лимитов
function refreshLimits() {
    loadLimitsInfo();
}

// Функция подтверждения для переключения лимитов
function confirmToggleLimits(checkbox) {
    const newState = checkbox.checked;

    if (newState) {
        if (!confirm('Вы уверены, что хотите ОТКЛЮЧИТЬ все лимиты рекламы?\n\nЭто позволит пользователям смотреть неограниченное количество рекламы.')) {
            checkbox.checked = false;
            return false;
        }
    }

    return true;
}

// Функция для тестирования сессии
function testSession() {
    fetch('test_session.php', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Данные сессии:', data);
        alert('Данные сессии выведены в консоль браузера (F12)');
    })
    .catch(error => {
        console.error('Ошибка тестирования сессии:', error);
        alert('Ошибка тестирования сессии: ' + error.message);
    });
}

// Загружаем данные при переключении на вкладку лимитов
document.addEventListener('DOMContentLoaded', function() {
    const limitsTab = document.getElementById('limits-tab');
    if (limitsTab) {
        limitsTab.addEventListener('shown.bs.tab', function() {
            loadLimitsSettings(); // Загружаем настройки переключателя (из встроенных данных)
            loadLimitsInfo();     // Затем загружаем информацию о лимитах
        });
    }

    // Автообновление каждые 30 секунд, если вкладка активна
    setInterval(function() {
        const limitsPane = document.getElementById('limits');
        if (limitsPane && limitsPane.classList.contains('active')) {
            loadLimitsInfo();     // Обновляем только лимиты (настройки не меняются без перезагрузки)
        }
    }, 30000);
});
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
