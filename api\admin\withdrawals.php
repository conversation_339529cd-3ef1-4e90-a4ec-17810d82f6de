<?php
/**
 * api/admin/withdrawals.php
 * Подробные отчёты по выводам средств
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/withdrawals.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/withdrawals.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../NOWPaymentsAPI.php')) { 
    http_response_code(500); 
    error_log('FATAL: NOWPaymentsAPI.php not found in admin/withdrawals.php'); 
    die('Ошибка: Не удалось загрузить NOWPaymentsAPI.php'); 
}
// --- Конец проверки зависимостей ---

// Параметры поиска и фильтрации
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$currency_filter = $_GET['currency'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$user_filter = $_GET['user'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Создаем экземпляр API для проверки статусов
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ СТАТУСОВ ОТКЛЮЧЕНО ДЛЯ ОТЛАДКИ
$statusUpdated = false;
$updatedCount = 0;

// Собираем все выводы
$allWithdrawals = [];
$currencies = [];
$statuses = [];

foreach ($userData as $userId => $user) {
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            // Добавляем информацию о пользователе
            $withdrawal['user_id'] = $userId;
            $withdrawal['user_name'] = $user['first_name'] ?? ($user['telegram_data']['first_name'] ?? 'Неизвестно');
            $withdrawal['user_lastname'] = $user['last_name'] ?? ($user['telegram_data']['last_name'] ?? '');
            $withdrawal['username'] = $user['username'] ?? ($user['telegram_data']['username'] ?? '');
            $withdrawal['withdrawal_index'] = $index;

            // Собираем уникальные валюты и статусы
            if (isset($withdrawal['currency'])) {
                $currencies[$withdrawal['currency']] = true;
            }
            if (isset($withdrawal['status'])) {
                $statuses[$withdrawal['status']] = true;
            }

            $allWithdrawals[] = $withdrawal;
        }
    }
}

// Сортируем по времени (новые сначала)
usort($allWithdrawals, function($a, $b) {
    return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
});

// Применяем фильтры
$filteredWithdrawals = array_filter($allWithdrawals, function($withdrawal) use ($search, $status_filter, $currency_filter, $date_from, $date_to, $user_filter) {
    // Поиск по тексту
    if (!empty($search)) {
        $searchText = strtolower($search);
        $searchFields = [
            strtolower($withdrawal['user_name'] ?? ''),
            strtolower($withdrawal['user_lastname'] ?? ''),
            strtolower($withdrawal['username'] ?? ''),
            strtolower($withdrawal['user_id'] ?? ''),
            strtolower($withdrawal['address'] ?? ''),
            strtolower($withdrawal['wallet_address'] ?? ''),
            strtolower($withdrawal['currency'] ?? ''),
            strtolower($withdrawal['payout_id'] ?? ''),
            strtolower($withdrawal['id'] ?? ''),
            strtolower($withdrawal['transaction_hash'] ?? ''),
            strtolower($withdrawal['txid'] ?? ''),
            strtolower($withdrawal['transaction_id'] ?? ''),
            strtolower($withdrawal['nowpayments_id'] ?? ''),
        ];
        
        $found = false;
        foreach ($searchFields as $field) {
            if (strpos($field, $searchText) !== false) {
                $found = true;
                break;
            }
        }
        if (!$found) return false;
    }
    
    // Фильтр по статусу
    if (!empty($status_filter) && ($withdrawal['status'] ?? '') !== $status_filter) {
        return false;
    }
    
    // Фильтр по валюте
    if (!empty($currency_filter) && ($withdrawal['currency'] ?? '') !== $currency_filter) {
        return false;
    }
    
    // Фильтр по пользователю
    if (!empty($user_filter) && ($withdrawal['user_id'] ?? '') !== $user_filter) {
        return false;
    }
    
    // Фильтр по дате
    if (!empty($date_from)) {
        $from_timestamp = strtotime($date_from);
        if (($withdrawal['timestamp'] ?? 0) < $from_timestamp) {
            return false;
        }
    }
    
    if (!empty($date_to)) {
        $to_timestamp = strtotime($date_to . ' 23:59:59');
        if (($withdrawal['timestamp'] ?? 0) > $to_timestamp) {
            return false;
        }
    }
    
    return true;
});

// Пагинация
$total_withdrawals = count($filteredWithdrawals);
$total_pages = ceil($total_withdrawals / $per_page);
$offset = ($page - 1) * $per_page;
$withdrawals_page = array_slice($filteredWithdrawals, $offset, $per_page);

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">📊 Подробные отчёты по выводам средств</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="withdrawals.php" class="btn btn-sm btn-outline-secondary">🔄 Обновить</a>
                        <button class="btn btn-sm btn-outline-info" onclick="forceUpdateStatuses()">⚡ Обновить статусы</button>
                        <button class="btn btn-sm btn-outline-warning" onclick="fixDuplicates()">🔧 Исправить дубликаты</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">📥 Экспорт CSV</button>
                        <?php if ($statusUpdated): ?>
                            <span class="badge bg-warning ms-2">Обновлено: <?php echo $updatedCount; ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Статистика -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Всего выводов</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_withdrawals; ?></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Сумма (монеты)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo array_sum(array_column($filteredWithdrawals, 'coins_amount')); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Валют</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($currencies); ?></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Пользователей</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo count(array_unique(array_column($filteredWithdrawals, 'user_id'))); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Фильтры и поиск -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">🔍 Поиск и фильтры</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="withdrawals.php">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">🔎 Поиск</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="ID, имя, адрес, валюта...">
                                <small class="form-text text-muted">Поиск по всем полям</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="status" class="form-label">📊 Статус</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">Все статусы</option>
                                    <?php foreach (array_keys($statuses) as $status): ?>
                                        <option value="<?php echo htmlspecialchars($status); ?>"
                                                <?php echo $status_filter === $status ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($status); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="currency" class="form-label">💰 Валюта</label>
                                <select class="form-control" id="currency" name="currency">
                                    <option value="">Все валюты</option>
                                    <?php foreach (array_keys($currencies) as $currency): ?>
                                        <option value="<?php echo htmlspecialchars($currency); ?>"
                                                <?php echo $currency_filter === $currency ? 'selected' : ''; ?>>
                                            <?php echo strtoupper($currency); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="date_from" class="form-label">📅 С даты</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="date_to" class="form-label">📅 По дату</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            <div class="col-md-1 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-sm">🔍 Найти</button>
                                    <a href="withdrawals.php" class="btn btn-secondary btn-sm">🗑️ Сброс</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Таблица выводов -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        💸 Список выводов (<?php echo $total_withdrawals; ?> записей)
                    </h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success btn-sm" onclick="runAutoRefundSystem()" title="Запустить автоматический возврат средств">
                            💰 Автовозврат
                        </button>
                        <button class="btn btn-info btn-sm" onclick="forceUpdateStatuses()" title="Принудительно обновить все статусы">
                            🔄 Обновить статусы
                        </button>
                        <div class="ms-2">
                            Страница <?php echo $page; ?> из <?php echo $total_pages; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($withdrawals_page)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox" style="font-size: 3rem; color: #ccc;"></i>
                            <p class="mt-2 text-muted">Выводы не найдены</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="withdrawalsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>🆔 ID</th>
                                        <th>👤 Пользователь</th>
                                        <th>💰 Сумма</th>
                                        <th>🏦 Валюта</th>
                                        <th>📍 Адрес</th>
                                        <th>📊 Статус</th>
                                        <th>📅 Дата</th>
                                        <th>🔗 NOWPayments</th>
                                        <th>⚙️ Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdrawals_page as $withdrawal): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($withdrawal['user_id']); ?></strong>
                                                <br><small class="text-muted">#<?php echo $withdrawal['withdrawal_index']; ?></small>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>
                                                        <?php echo htmlspecialchars($withdrawal['user_name']); ?>
                                                        <?php if (!empty($withdrawal['user_lastname'])): ?>
                                                            <?php echo htmlspecialchars($withdrawal['user_lastname']); ?>
                                                        <?php endif; ?>
                                                    </strong>
                                                </div>
                                                <?php if (!empty($withdrawal['username'])): ?>
                                                    <small class="text-muted">@<?php echo htmlspecialchars($withdrawal['username']); ?></small>
                                                <?php endif; ?>
                                                <br><small class="text-info">ID: <?php echo htmlspecialchars($withdrawal['user_id']); ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($withdrawal['coins_amount'] ?? 0, 2); ?></strong> монет
                                                <?php if (isset($withdrawal['crypto_amount'])): ?>
                                                    <br><small class="text-muted">
                                                        ≈ <?php echo number_format($withdrawal['crypto_amount'], 8); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo strtoupper($withdrawal['currency'] ?? 'N/A'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <code style="font-size: 0.8rem;">
                                                    <?php
                                                    $address = $withdrawal['wallet_address'] ?? $withdrawal['address'] ?? 'N/A';
                                                    echo htmlspecialchars(strlen($address) > 20 ? substr($address, 0, 20) . '...' : $address);
                                                    ?>
                                                </code>
                                                <?php if (isset($address) && strlen($address) > 20): ?>
                                                    <br><small>
                                                        <a href="#" onclick="copyToClipboard('<?php echo htmlspecialchars($address); ?>')">
                                                            📋 Копировать полный
                                                        </a>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status = $withdrawal['status'] ?? 'unknown';
                                                $statusClass = 'secondary';
                                                $statusIcon = '❓';

                                                switch (strtolower($status)) {
                                                    // Статусы NOWPayments
                                                    case 'waiting':
                                                        $statusClass = 'warning';
                                                        $statusIcon = '⏳';
                                                        $statusText = 'Ожидание обработки';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'info';
                                                        $statusIcon = '⚙️';
                                                        $statusText = 'Обрабатывается';
                                                        break;
                                                    case 'sending':
                                                        $statusClass = 'primary';
                                                        $statusIcon = '📤';
                                                        $statusText = 'Отправляется на кошелек';
                                                        break;
                                                    case 'finished':
                                                        $statusClass = 'success';
                                                        $statusIcon = '✅';
                                                        $statusText = 'Отправлено на кошелек';
                                                        break;
                                                    case 'confirmed':
                                                        $statusClass = 'success';
                                                        $statusIcon = '✅';
                                                        $statusText = 'Подтверждено в блокчейне';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'success';
                                                        $statusIcon = '✅';
                                                        $statusText = 'Завершено';
                                                        break;
                                                    case 'failed':
                                                        $statusClass = 'danger';
                                                        $statusIcon = '❌';
                                                        $statusText = 'Ошибка выплаты';
                                                        break;
                                                    case 'rejected':
                                                        $statusClass = 'danger';
                                                        $statusIcon = '🚫';
                                                        $statusText = 'Отклонено системой';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'dark';
                                                        $statusIcon = '🚫';
                                                        $statusText = 'Отменено';
                                                        break;
                                                    case 'expired':
                                                        $statusClass = 'secondary';
                                                        $statusIcon = '⏰';
                                                        $statusText = 'Истекло';
                                                        break;
                                                    // Старые статусы для совместимости
                                                    case 'pending':
                                                        $statusClass = 'warning';
                                                        $statusIcon = '⏳';
                                                        $statusText = 'В обработке';
                                                        break;
                                                    case 'success':
                                                    case 'error':
                                                        $statusClass = $status === 'success' ? 'success' : 'danger';
                                                        $statusIcon = $status === 'success' ? '✅' : '❌';
                                                        $statusText = $status === 'success' ? 'Успех' : 'Ошибка';
                                                        break;
                                                    default:
                                                        $statusClass = 'secondary';
                                                        $statusIcon = '❓';
                                                        $statusText = ucfirst($status);
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $statusClass; ?>" title="<?php echo htmlspecialchars($statusText ?? $status); ?>">
                                                    <?php echo $statusIcon; ?> <?php echo htmlspecialchars($statusText ?? $status); ?>
                                                </span>

                                                <?php
                                                // Показываем информацию о возврате для отклоненных выплат
                                                $isRefunded = $withdrawal['refunded'] ?? false;
                                                $refundAmount = $withdrawal['refund_amount'] ?? 0;
                                                $refundDate = $withdrawal['refund_date'] ?? null;

                                                if ($isRefunded && $refundAmount > 0): ?>
                                                    <br><small class="badge bg-success mt-1" title="Средства возвращены пользователю">
                                                        💰 Возвращено: <?php echo $refundAmount; ?> монет
                                                    </small>
                                                    <?php if ($refundDate): ?>
                                                        <br><small class="text-muted" style="font-size: 0.7rem;">
                                                            📅 <?php echo date('d.m.Y H:i', strtotime($refundDate)); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                <?php elseif (in_array(strtolower($status), ['rejected', 'failed', 'cancelled', 'expired']) && !$isRefunded): ?>
                                                    <br><small class="badge bg-warning mt-1" title="Требуется возврат средств">
                                                        ⚠️ Возврат не выполнен
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($withdrawal['timestamp'])): ?>
                                                    <div><?php echo date('d.m.Y', $withdrawal['timestamp']); ?></div>
                                                    <small class="text-muted"><?php echo date('H:i:s', $withdrawal['timestamp']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $payoutId = $withdrawal['payout_id'] ?? $withdrawal['nowpayments_id'] ?? null;
                                                $transactionHash = $withdrawal['transaction_hash'] ?? $withdrawal['txid'] ?? $withdrawal['transaction_id'] ?? null;
                                                ?>
                                                <?php if ($payoutId): ?>
                                                    <div>
                                                        <strong>Payout ID:</strong>
                                                        <code><?php echo htmlspecialchars($payoutId); ?></code>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if ($transactionHash): ?>
                                                    <div>
                                                        <strong>TX Hash:</strong>
                                                        <code style="font-size: 0.7rem;">
                                                            <?php
                                                            echo htmlspecialchars(strlen($transactionHash) > 15 ? substr($transactionHash, 0, 15) . '...' : $transactionHash);
                                                            ?>
                                                        </code>
                                                        <?php if (strlen($transactionHash) > 15): ?>
                                                            <br><small>
                                                                <a href="#" onclick="copyToClipboard('<?php echo htmlspecialchars($transactionHash); ?>')">
                                                                    📋 Копировать полный
                                                                </a>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!$payoutId && !$transactionHash): ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <?php if ($payoutId): ?>
                                                        <button class="btn btn-outline-info btn-sm"
                                                                onclick="checkPayoutStatus('<?php echo htmlspecialchars($payoutId); ?>')">
                                                            🔍 Статус
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($transactionHash): ?>
                                                        <button class="btn btn-outline-success btn-sm"
                                                                onclick="openBlockchainExplorer('<?php echo htmlspecialchars($transactionHash); ?>', '<?php echo htmlspecialchars($withdrawal['currency'] ?? 'eth'); ?>')">
                                                            🔗 Блокчейн
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-primary btn-sm"
                                                            onclick="showWithdrawalDetails(<?php echo htmlspecialchars(json_encode($withdrawal, JSON_HEX_APOS | JSON_HEX_QUOT)); ?>)">
                                                        📋 Детали
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Пагинация -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Навигация по страницам">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                ← Предыдущая
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                Следующая →
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Модальное окно для деталей вывода -->
<div class="modal fade" id="withdrawalDetailsModal" tabindex="-1" aria-labelledby="withdrawalDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="withdrawalDetailsModalLabel">📋 Детали вывода средств</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="withdrawalDetailsContent">
                <!-- Содержимое будет загружено через JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно для статуса выплаты -->
<div class="modal fade" id="payoutStatusModal" tabindex="-1" aria-labelledby="payoutStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="payoutStatusModalLabel">🔍 Статус выплаты NOWPayments</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="payoutStatusContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <p class="mt-2">Проверяем статус выплаты...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
            </div>
        </div>
    </div>
</div>

<script>
// Функция для показа деталей вывода
function showWithdrawalDetails(withdrawal) {
    const content = document.getElementById('withdrawalDetailsContent');

    const formatDate = (timestamp) => {
        if (!timestamp) return 'N/A';
        const date = new Date(timestamp * 1000);
        return date.toLocaleString('ru-RU');
    };

    const formatAmount = (amount) => {
        return amount ? parseFloat(amount).toFixed(8) : 'N/A';
    };

    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>👤 Информация о пользователе</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID пользователя:</strong></td><td>${withdrawal.user_id || 'N/A'}</td></tr>
                    <tr><td><strong>Имя:</strong></td><td>${withdrawal.user_name || 'N/A'} ${withdrawal.user_lastname || ''}</td></tr>
                    <tr><td><strong>Username:</strong></td><td>${withdrawal.username ? '@' + withdrawal.username : 'N/A'}</td></tr>
                    <tr><td><strong>Индекс вывода:</strong></td><td>#${withdrawal.withdrawal_index || 'N/A'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>💰 Информация о выводе</h6>
                <table class="table table-sm">
                    <tr><td><strong>Сумма (монеты):</strong></td><td>${formatAmount(withdrawal.coins_amount)}</td></tr>
                    <tr><td><strong>Сумма (крипто):</strong></td><td>${formatAmount(withdrawal.crypto_amount)}</td></tr>
                    <tr><td><strong>Валюта:</strong></td><td>${(withdrawal.currency || 'N/A').toUpperCase()}</td></tr>
                    <tr><td><strong>Статус:</strong></td><td><span class="badge bg-info">${withdrawal.status || 'N/A'}</span></td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6>🏦 Информация о транзакции</h6>
                <table class="table table-sm">
                    <tr><td><strong>Адрес получателя:</strong></td><td><code>${withdrawal.wallet_address || withdrawal.address || 'N/A'}</code></td></tr>
                    <tr><td><strong>Payout ID:</strong></td><td><code>${withdrawal.payout_id || withdrawal.id || 'N/A'}</code></td></tr>
                    <tr><td><strong>Transaction Hash:</strong></td><td><code>${withdrawal.transaction_hash || withdrawal.txid || withdrawal.transaction_id || 'N/A'}</code></td></tr>
                    <tr><td><strong>Дата создания:</strong></td><td>${formatDate(withdrawal.timestamp)}</td></tr>
                </table>
            </div>
        </div>

        ${withdrawal.error_message ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>❌ Информация об ошибке</h6>
                <div class="alert alert-danger">
                    ${withdrawal.error_message}
                </div>
            </div>
        </div>
        ` : ''}

        ${withdrawal.refunded ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>💰 Информация о возврате средств</h6>
                <div class="alert alert-success">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Статус возврата:</strong> ✅ Возвращено<br>
                            <strong>Сумма возврата:</strong> ${withdrawal.refund_amount || 'N/A'} монет<br>
                        </div>
                        <div class="col-md-6">
                            <strong>Дата возврата:</strong> ${withdrawal.refund_date || 'N/A'}<br>
                            <strong>Причина:</strong> ${withdrawal.refund_reason || 'Автоматический возврат'}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        ` : (withdrawal.status && ['rejected', 'failed', 'cancelled', 'expired'].includes(withdrawal.status.toLowerCase()) ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>⚠️ Статус возврата средств</h6>
                <div class="alert alert-warning">
                    <strong>Внимание:</strong> Выплата отклонена, но возврат средств не выполнен!<br>
                    <small>Рекомендуется проверить и выполнить возврат ${withdrawal.coins_amount || 'N/A'} монет пользователю.</small>
                </div>
            </div>
        </div>
        ` : '')}

        <div class="row mt-3">
            <div class="col-12">
                <h6>🔧 Полные данные (JSON)</h6>
                <pre class="bg-light p-3" style="max-height: 200px; overflow-y: auto;"><code>${JSON.stringify(withdrawal, null, 2)}</code></pre>
            </div>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('withdrawalDetailsModal'));
    modal.show();
}

// Функция для проверки статуса выплаты
function checkPayoutStatus(payoutId) {
    const modal = new bootstrap.Modal(document.getElementById('payoutStatusModal'));
    modal.show();

    // Отправляем AJAX запрос для проверки статуса
    fetch('check_payout_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ payout_id: payoutId })
    })
    .then(response => response.json())
    .then(data => {
        const content = document.getElementById('payoutStatusContent');

        if (data.success) {
            const payout = data.payout;
            content.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <h6>📊 Статус выплаты</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID выплаты:</strong></td><td><code>${payout.id || 'N/A'}</code></td></tr>
                            <tr><td><strong>Статус:</strong></td><td><span class="badge bg-info">${payout.status || 'N/A'}</span></td></tr>
                            <tr><td><strong>Сумма:</strong></td><td>${payout.amount || 'N/A'} ${(payout.currency || '').toUpperCase()}</td></tr>
                            <tr><td><strong>Адрес:</strong></td><td><code>${payout.address || 'N/A'}</code></td></tr>
                            <tr><td><strong>Дата создания:</strong></td><td>${payout.created_at || 'N/A'}</td></tr>
                            <tr><td><strong>Дата обновления:</strong></td><td>${payout.updated_at || 'N/A'}</td></tr>
                        </table>

                        ${payout.hash ? `
                        <div class="mt-3">
                            <h6>🔗 Транзакция</h6>
                            <p><strong>Hash:</strong> <code>${payout.hash}</code></p>
                        </div>
                        ` : ''}

                        ${payout.error ? `
                        <div class="mt-3">
                            <h6>❌ Ошибка</h6>
                            <div class="alert alert-danger">${payout.error}</div>
                        </div>
                        ` : ''}

                        <div class="mt-3">
                            <h6>🔧 Полный ответ API</h6>
                            <pre class="bg-light p-3" style="max-height: 200px; overflow-y: auto;"><code>${JSON.stringify(payout, null, 2)}</code></pre>
                        </div>
                    </div>
                </div>
            `;
        } else {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <h6>❌ Ошибка при проверке статуса</h6>
                    <p>${data.message || 'Неизвестная ошибка'}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        const content = document.getElementById('payoutStatusContent');
        content.innerHTML = `
            <div class="alert alert-danger">
                <h6>❌ Ошибка сети</h6>
                <p>Не удалось проверить статус выплаты: ${error.message}</p>
            </div>
        `;
    });
}

// Функция для копирования в буфер обмена
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Показываем уведомление
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0';
        toast.style.position = 'fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    📋 Скопировано в буфер обмена!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Удаляем элемент после скрытия
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        alert('Ошибка при копировании: ' + err);
    });
}

// Функция для принудительного обновления статусов
function forceUpdateStatuses() {
    const button = event.target;
    const originalText = button.textContent;

    button.disabled = true;
    button.textContent = '⏳ Обновляем...';

    fetch('../force_update_withdrawals.php?json=1')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`✅ Статусы обновлены!\nПроверено: ${data.checked}\nОбновлено: ${data.updated}`);
                if (data.updated > 0) {
                    location.reload(); // Перезагружаем страницу если были обновления
                }
            } else {
                alert(`❌ Ошибка: ${data.error}`);
            }
        })
        .catch(error => {
            alert(`❌ Ошибка сети: ${error.message}`);
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = originalText;
        });
}

// Функция для запуска системы автоматического возврата
function runAutoRefundSystem() {
    const button = event.target;
    const originalText = button.textContent;

    if (!confirm('Запустить систему автоматического возврата средств?\n\nЭто проверит все отклоненные выплаты и вернет средства пользователям.')) {
        return;
    }

    button.disabled = true;
    button.textContent = '⏳ Обрабатываем...';

    fetch('../auto_refund_system.php?json=1')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`✅ Автовозврат завершен!\n\n📊 Проверено выплат: ${data.checked}\n🔄 Обновлено статусов: ${data.updated}\n💰 Возвращено выплат: ${data.refunded}\n💎 Сумма возврата: ${data.refund_amount} монет`);
                if (data.updated > 0 || data.refunded > 0) {
                    location.reload(); // Перезагружаем страницу если были изменения
                }
            } else {
                alert(`❌ Ошибка автовозврата: ${data.error}`);
            }
        })
        .catch(error => {
            alert(`❌ Ошибка сети: ${error.message}`);
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = originalText;
        });
}

// Функция для исправления дубликатов
function fixDuplicates() {
    if (!confirm('Вы уверены, что хотите исправить дублирующиеся записи выплат?\n\nЭто действие удалит повторяющиеся записи с одинаковыми payout_id.')) {
        return;
    }

    const button = event.target;
    const originalText = button.textContent;

    button.disabled = true;
    button.textContent = '⏳ Исправляем...';

    fetch('fix_duplicates.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.total_duplicates > 0) {
                    alert(`✅ Дубликаты исправлены!\nУдалено дубликатов: ${data.total_duplicates}\nПользователей исправлено: ${data.users_fixed}`);
                    location.reload(); // Перезагружаем страницу
                } else {
                    alert('✅ Дубликаты не найдены. Все записи уникальны.');
                }
            } else {
                alert(`❌ Ошибка: ${data.error}`);
            }
        })
        .catch(error => {
            alert(`❌ Ошибка сети: ${error.message}`);
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = originalText;
        });
}

// Функция для открытия блокчейн эксплорера
function openBlockchainExplorer(hash, currency) {
    let url = '';

    switch (currency.toLowerCase()) {
        case 'eth':
        case 'ethereum':
            url = `https://etherscan.io/tx/${hash}`;
            break;
        case 'btc':
        case 'bitcoin':
            url = `https://blockstream.info/tx/${hash}`;
            break;
        case 'usdttrc20':
        case 'ton':
        case 'telegram':
            url = `https://tonscan.org/tx/${hash}`;
            break;
        case 'ltc':
        case 'litecoin':
            url = `https://blockchair.com/litecoin/transaction/${hash}`;
            break;
        case 'bch':
            url = `https://blockchair.com/bitcoin-cash/transaction/${hash}`;
            break;
        case 'xrp':
        case 'ripple':
            url = `https://xrpscan.com/tx/${hash}`;
            break;
        case 'ada':
        case 'cardano':
            url = `https://cardanoscan.io/transaction/${hash}`;
            break;
        case 'dot':
        case 'polkadot':
            url = `https://polkascan.io/polkadot/transaction/${hash}`;
            break;
        default:
            url = `https://etherscan.io/tx/${hash}`;
    }

    window.open(url, '_blank');
}

// Функция для экспорта в CSV
function exportToCSV() {
    const table = document.getElementById('withdrawalsTable');
    const rows = table.querySelectorAll('tr');

    let csv = [];

    // Заголовки
    const headers = [];
    rows[0].querySelectorAll('th').forEach(th => {
        headers.push('"' + th.textContent.replace(/"/g, '""') + '"');
    });
    csv.push(headers.join(','));

    // Данные
    for (let i = 1; i < rows.length; i++) {
        const row = [];
        rows[i].querySelectorAll('td').forEach(td => {
            row.push('"' + td.textContent.replace(/"/g, '""').replace(/\n/g, ' ').trim() + '"');
        });
        csv.push(row.join(','));
    }

    // Создаем и скачиваем файл
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'withdrawals_' + new Date().toISOString().slice(0, 10) + '.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
