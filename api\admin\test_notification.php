<?php
/**
 * api/admin/test_notification.php
 * API для тестовой отправки уведомления
 */

header('Content-Type: application/json');

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Не авторизован']);
    exit;
}

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Метод не разрешен']);
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions.php';

// Подключаем функции для работы с текстами бота
if (file_exists(__DIR__ . '/../../bot/setup_bot_menu.php')) {
    require_once __DIR__ . '/../../bot/setup_bot_menu.php';
}

try {
    // Получаем настройки уведомлений из JSON
    $settingsFile = __DIR__ . '/../cron/notification_settings.json';
    $settings = [];

    if (file_exists($settingsFile)) {
        $content = file_get_contents($settingsFile);
        $settingsData = json_decode($content, true);
        if ($settingsData) {
            foreach ($settingsData as $key => $data) {
                $settings[$key] = $data['value'] ?? '';
            }
        }
    }

    // Настройки по умолчанию если файла нет
    if (empty($settings)) {
        $settings = [
            'message_template' => 'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!',
            'bot_username' => 'uniqpaid_paid_bot'
        ];
    }
    
    // Получаем токен бота из конфигурации
    if (!defined('TELEGRAM_BOT_TOKEN') || empty(TELEGRAM_BOT_TOKEN)) {
        echo json_encode(['success' => false, 'error' => 'Токен Telegram бота не настроен']);
        exit;
    }
    
    // Ищем пользователя @alter_mega_ego (тебя) для тестовой отправки
    $userData = loadUserData();
    $testUser = null;

    if ($userData && is_array($userData)) {
        foreach ($userData as $userId => $user) {
            // Ищем пользователя @alter_mega_ego
            if (isset($user['username']) && $user['username'] === 'alter_mega_ego') {
                $testUser = [
                    'telegram_id' => $user['id'] ?? $userId,
                    'first_name' => $user['first_name'] ?? 'Альтер',
                    'last_name' => $user['last_name'] ?? 'Эго',
                    'username' => $user['username'] ?? 'alter_mega_ego'
                ];
                break; // Нашли тебя!
            }
        }
    }

    if (!$testUser) {
        // Если не нашли, используем твои данные напрямую
        $testUser = [
            'telegram_id' => '5880288830', // Твой Telegram ID
            'first_name' => 'Альтер',
            'last_name' => 'Эго',
            'username' => 'alter_mega_ego'
        ];
    }
    
    // Формируем сообщение из шаблона
    $messageTemplate = $settings['message_template'];
    $message = str_replace(
        ['{first_name}', '{last_name}', '{username}'],
        [
            $testUser['first_name'] ?: 'Пользователь',
            $testUser['last_name'] ?: '',
            $testUser['username'] ? '@' . $testUser['username'] : ''
        ],
        $messageTemplate
    );
    
    // Заменяем \n на реальные переносы строк
    $message = str_replace('\\n', "\n", $message);

    // РЕАЛЬНАЯ ОТПРАВКА через Telegram Bot API с кнопкой
    $telegramUrl = "https://api.telegram.org/bot" . TELEGRAM_BOT_TOKEN . "/sendMessage";

    // Определяем язык пользователя (русский по умолчанию)
    $userLang = 'ru'; // По умолчанию русский
    if (isset($userData[$testUser['telegram_id']]['language'])) {
        $userLang = $userData[$testUser['telegram_id']]['language'];
    }

    // Тексты кнопок из файла bot_texts.json или fallback
    if (function_exists('getBotText')) {
        $launchAppText = getBotText('buttons.launch_app', $userLang);
        $friendsText = getBotText('buttons.friends', $userLang);
        $balanceText = getBotText('buttons.my_balance', $userLang);
        $statsText = getBotText('buttons.statistics', $userLang);
        $helpText = getBotText('buttons.help', $userLang);
    } else {
        // Fallback тексты если функция недоступна
        if ($userLang === 'en') {
            $launchAppText = '🚀 Launch App';
            $friendsText = '👥 Friends';
            $balanceText = '💰 My Balance';
            $statsText = '📊 Statistics';
            $helpText = '❓ Help';
        } else {
            $launchAppText = '🚀 Запустить приложение';
            $friendsText = '👥 Друзья';
            $balanceText = '💰 Мой баланс';
            $statsText = '📊 Статистика';
            $helpText = '❓ Помощь';
        }
    }

    // Полная inline клавиатура как в основном боте
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => $launchAppText,
                    'web_app' => [
                        'url' => 'https://app.uniqpaid.com/test3/'
                    ]
                ]
            ],
            [
                [
                    'text' => $friendsText,
                    'callback_data' => 'invite_friends'
                ],
                [
                    'text' => $balanceText,
                    'callback_data' => 'my_balance'
                ]
            ],
            [
                [
                    'text' => $statsText,
                    'callback_data' => 'my_stats'
                ],
                [
                    'text' => $helpText,
                    'callback_data' => 'help'
                ]
            ]
        ]
    ];

    $postData = [
        'chat_id' => $testUser['telegram_id'],
        'text' => $message,
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => true,
        'reply_markup' => json_encode($keyboard)
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($postData),
            'timeout' => 10
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($telegramUrl, false, $context);
    $response = json_decode($result, true);

    $status = 'failed';
    $errorMessage = null;

    if (isset($response['ok']) && $response['ok']) {
        $status = 'sent';
        $logMessage = "✅ Тестовое уведомление ОТПРАВЛЕНО: {$testUser['first_name']} (@{$testUser['username']})";
    } else {
        $errorMessage = isset($response['description']) ? $response['description'] : 'Unknown error';
        $logMessage = "❌ Ошибка отправки тестового уведомления: {$errorMessage}";
    }

    error_log($logMessage);
    
    // Записываем тестовое уведомление в JSON лог
    $logsFile = __DIR__ . '/../cron/notification_logs.json';
    $logs = [];

    if (file_exists($logsFile)) {
        $content = file_get_contents($logsFile);
        $logs = json_decode($content, true) ?: [];
    }

    $logEntry = [
        'id' => 'test_' . uniqid(),
        'user_id' => $testUser['telegram_id'], // Твой ID
        'telegram_id' => $testUser['telegram_id'],
        'username' => $testUser['username'],
        'first_name' => $testUser['first_name'],
        'last_name' => $testUser['last_name'],
        'message_text' => $message,
        'status' => $status,
        'error_message' => $errorMessage,
        'last_activity' => time(),
        'sent_at' => time()
    ];

    $logs[] = $logEntry;

    // Сохраняем обновленные логи
    file_put_contents($logsFile, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), LOCK_EX);
    
    echo json_encode([
        'success' => $status === 'sent',
        'message' => $status === 'sent' ?
            'Тестовое уведомление ОТПРАВЛЕНО тебе в Telegram!' :
            'Ошибка отправки: ' . $errorMessage,
        'test_data' => [
            'recipient' => $testUser['first_name'] . ' ' . ($testUser['last_name'] ?: '') . ' (@' . $testUser['username'] . ')',
            'telegram_id' => $testUser['telegram_id'],
            'message_preview' => substr($message, 0, 100) . '...',
            'status' => $status,
            'sent_to_telegram' => $status === 'sent'
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Ошибка: ' . $e->getMessage()]);
}
?>
