<?php
/**
 * api/security.php
 * Функции безопасности для защиты от мошенничества и накрутки монет
 */

// Константы для ограничений
define('MAX_AD_VIEWS_PER_HOUR', 60); // Общий лимит просмотров рекламы в час (для совместимости)
define('MAX_AD_VIEWS_PER_DAY', 300); // Общий лимит просмотров рекламы в день (для совместимости)
define('MAX_WITHDRAWALS_PER_DAY', 999999); // Лимиты на выводы отключены
define('SUSPICIOUS_ACTIVITY_THRESHOLD', 5); // Порог подозрительной активности
define('MAX_REQUESTS_PER_MINUTE', 30); // Максимальное количество запросов с одного IP в минуту

// Лимиты для каждого типа рекламы отдельно (20 показов каждого типа в день)
define('MAX_AD_VIEWS_PER_TYPE_PER_DAY', 20); // Лимит для каждого типа рекламы в день
define('MAX_AD_VIEWS_PER_TYPE_PER_HOUR', 20); // Лимит для каждого типа рекламы в час

/**
 * Проверяет лимит запросов с одного IP адреса
 *
 * @param string $ip IP адрес клиента
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkIpRateLimit($ip) {
    $rateLimitFile = __DIR__ . '/rate_limit.json';
    $currentTime = time();
    $minuteAgo = $currentTime - 60;

    // Загружаем данные о запросах
    $rateLimitData = [];
    if (file_exists($rateLimitFile)) {
        $jsonData = file_get_contents($rateLimitFile);
        if ($jsonData !== false) {
            $rateLimitData = json_decode($jsonData, true) ?: [];
        }
    }

    // Инициализируем данные для IP, если их нет
    if (!isset($rateLimitData[$ip])) {
        $rateLimitData[$ip] = [];
    }

    // Фильтруем запросы за последнюю минуту
    $rateLimitData[$ip] = array_filter($rateLimitData[$ip], function($timestamp) use ($minuteAgo) {
        return $timestamp >= $minuteAgo;
    });

    // Проверяем лимит
    if (count($rateLimitData[$ip]) >= MAX_REQUESTS_PER_MINUTE) {
        error_log("security WARNING: IP {$ip} превысил лимит запросов в минуту");
        return false;
    }

    // Добавляем новый запрос
    $rateLimitData[$ip][] = $currentTime;

    // Очищаем старые данные (старше часа)
    $hourAgo = $currentTime - 3600;
    foreach ($rateLimitData as $checkIp => $timestamps) {
        $rateLimitData[$checkIp] = array_filter($timestamps, function($timestamp) use ($hourAgo) {
            return $timestamp >= $hourAgo;
        });

        // Удаляем пустые записи
        if (empty($rateLimitData[$checkIp])) {
            unset($rateLimitData[$checkIp]);
        }
    }

    // Сохраняем обновленные данные
    file_put_contents($rateLimitFile, json_encode($rateLimitData), LOCK_EX);

    return true;
}

/**
 * Проверяет, не превышен ли лимит просмотров рекламы определенного типа
 *
 * @param int $userId ID пользователя
 * @param string $adType Тип рекламы (native_banner, rewarded_video, interstitial)
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkAdViewLimitByType($userId, $adType, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита просмотров типа {$adType}");
        return false;
    }

    // Инициализируем массив для отслеживания просмотров рекламы по типам, если его еще нет
    if (!isset($userData[$userId]['ad_views_by_type'])) {
        $userData[$userId]['ad_views_by_type'] = [];
    }

    if (!isset($userData[$userId]['ad_views_by_type'][$adType])) {
        $userData[$userId]['ad_views_by_type'][$adType] = [];
    }

    $currentTime = time();
    $hourAgo = $currentTime - 3600;
    $dayAgo = $currentTime - 86400;

    // Фильтруем просмотры данного типа за последний час
    $hourViews = array_filter($userData[$userId]['ad_views_by_type'][$adType], function($timestamp) use ($hourAgo) {
        return $timestamp >= $hourAgo;
    });

    // Фильтруем просмотры данного типа за последний день
    $dayViews = array_filter($userData[$userId]['ad_views_by_type'][$adType], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });

    // Очищаем старые записи (старше суток)
    $userData[$userId]['ad_views_by_type'][$adType] = $dayViews;

    // Проверяем лимиты для данного типа рекламы
    if (count($hourViews) >= MAX_AD_VIEWS_PER_TYPE_PER_HOUR) {
        error_log("security WARNING: Пользователь {$userId} превысил часовой лимит просмотров рекламы типа {$adType} (" . count($hourViews) . "/" . MAX_AD_VIEWS_PER_TYPE_PER_HOUR . ")");
        return false;
    }

    if (count($dayViews) >= MAX_AD_VIEWS_PER_TYPE_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит просмотров рекламы типа {$adType} (" . count($dayViews) . "/" . MAX_AD_VIEWS_PER_TYPE_PER_DAY . ")");
        return false;
    }

    // Добавляем новый просмотр в лог для данного типа
    $userData[$userId]['ad_views_by_type'][$adType][] = $currentTime;

    error_log("security INFO: Просмотр рекламы типа {$adType} засчитан для пользователя {$userId}. Сегодня: " . (count($dayViews) + 1) . "/" . MAX_AD_VIEWS_PER_TYPE_PER_DAY);

    return true;
}

/**
 * Проверяет, не превышен ли общий лимит просмотров рекламы (для совместимости)
 *
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkAdViewLimit($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита просмотров");
        return false;
    }

    // Инициализируем массив для отслеживания просмотров рекламы, если его еще нет
    if (!isset($userData[$userId]['ad_views_log'])) {
        $userData[$userId]['ad_views_log'] = [];
    }

    $currentTime = time();
    $hourAgo = $currentTime - 3600;
    $dayAgo = $currentTime - 86400;

    // Фильтруем просмотры за последний час
    $hourViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($hourAgo) {
        return $timestamp >= $hourAgo;
    });

    // Фильтруем просмотры за последний день
    $dayViews = array_filter($userData[$userId]['ad_views_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });

    // Очищаем старые записи (старше суток)
    $userData[$userId]['ad_views_log'] = $dayViews;

    // Проверяем лимиты
    if (count($hourViews) >= MAX_AD_VIEWS_PER_HOUR) {
        error_log("security WARNING: Пользователь {$userId} превысил часовой лимит просмотров рекламы");
        return false;
    }

    if (count($dayViews) >= MAX_AD_VIEWS_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит просмотров рекламы");
        return false;
    }

    // Добавляем новый просмотр в лог
    $userData[$userId]['ad_views_log'][] = $currentTime;

    return true;
}

/**
 * Проверяет, не превышен ли лимит выводов средств
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если лимит не превышен, false в противном случае
 */
function checkWithdrawalLimit($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке лимита выводов");
        return false;
    }
    
    // Инициализируем массив для отслеживания выводов, если его еще нет
    if (!isset($userData[$userId]['withdrawal_log'])) {
        $userData[$userId]['withdrawal_log'] = [];
    }
    
    $currentTime = time();
    $dayAgo = $currentTime - 86400;
    
    // Фильтруем выводы за последний день
    $dayWithdrawals = array_filter($userData[$userId]['withdrawal_log'], function($timestamp) use ($dayAgo) {
        return $timestamp >= $dayAgo;
    });
    
    // Очищаем старые записи (старше суток)
    $userData[$userId]['withdrawal_log'] = $dayWithdrawals;
    
    // Проверяем лимит
    if (count($dayWithdrawals) >= MAX_WITHDRAWALS_PER_DAY) {
        error_log("security WARNING: Пользователь {$userId} превысил дневной лимит выводов средств");
        return false;
    }
    
    // Добавляем новый вывод в лог
    $userData[$userId]['withdrawal_log'][] = $currentTime;
    
    return true;
}

/**
 * Проверяет баланс пользователя перед выводом средств
 * 
 * @param int $userId ID пользователя
 * @param int $amount Сумма для вывода
 * @param array $userData Данные всех пользователей
 * @return bool true, если баланс достаточен, false в противном случае
 */
function verifyBalance($userId, $amount, &$userData) {
    if (!isset($userData[$userId]) || !isset($userData[$userId]['balance'])) {
        error_log("security ERROR: Пользователь {$userId} не найден или нет баланса при проверке баланса");
        return false;
    }
    
    $balance = $userData[$userId]['balance'];
    
    // Проверяем, достаточно ли средств
    if ($balance < $amount) {
        error_log("security WARNING: Недостаточно средств у пользователя {$userId}. Запрошено: {$amount}, Доступно: {$balance}");
        return false;
    }
    
    // ИСПРАВЛЕНИЕ: Убираем избыточную проверку total_earned vs total_withdrawn
    // Баланс пользователя уже корректно отражает доступные для вывода средства
    // Проверка total_earned может блокировать легитимные выводы из-за рассинхронизации данных

    error_log("security INFO: Проверка баланса пройдена для пользователя {$userId}. Запрошено: {$amount}, Доступно: {$balance}");

    return true;
}

/**
 * Записывает операцию в журнал аудита
 * 
 * @param string $operation Тип операции
 * @param int $userId ID пользователя
 * @param array $data Дополнительные данные
 * @return bool true в случае успеха, false при ошибке
 */
function logAuditEvent($operation, $userId, $data = []) {
    $logFile = __DIR__ . '/audit.log';
    
    $logEntry = [
        'timestamp' => time(),
        'operation' => $operation,
        'user_id' => $userId,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'data' => $data
    ];
    
    $logLine = date('Y-m-d H:i:s') . ' | ' . json_encode($logEntry) . PHP_EOL;
    
    return file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX) !== false;
}

/**
 * Проверяет, не является ли активность пользователя подозрительной
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @return bool true, если активность не подозрительная, false в противном случае
 */
function checkSuspiciousActivity($userId, &$userData) {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при проверке подозрительной активности");
        return false;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    // Если счетчик превышает порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        if (!isset($userData[$userId]['blocked']) || !$userData[$userId]['blocked']) {
            $userData[$userId]['blocked'] = true;
            $userData[$userId]['blocked_at'] = time();
            error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        }
        return false;
    }
    
    return true;
}

/**
 * Увеличивает счетчик подозрительной активности пользователя
 * 
 * @param int $userId ID пользователя
 * @param array $userData Данные всех пользователей
 * @param string $reason Причина увеличения счетчика
 * @return void
 */
function incrementSuspiciousActivity($userId, &$userData, $reason = '') {
    if (!isset($userData[$userId])) {
        error_log("security ERROR: Пользователь {$userId} не найден при увеличении счетчика подозрительной активности");
        return;
    }
    
    // Инициализируем счетчик подозрительной активности, если его еще нет
    if (!isset($userData[$userId]['suspicious_activity'])) {
        $userData[$userId]['suspicious_activity'] = 0;
    }
    
    $userData[$userId]['suspicious_activity']++;
    
    error_log("security WARNING: Увеличен счетчик подозрительной активности для пользователя {$userId}. Новое значение: {$userData[$userId]['suspicious_activity']}. Причина: {$reason}");
    
    // Логируем событие
    logAuditEvent('suspicious_activity', $userId, ['reason' => $reason, 'count' => $userData[$userId]['suspicious_activity']]);
    
    // Если счетчик превысил порог, блокируем пользователя
    if ($userData[$userId]['suspicious_activity'] >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
        $userData[$userId]['blocked'] = true;
        $userData[$userId]['blocked_at'] = time();
        error_log("security ALERT: Пользователь {$userId} заблокирован из-за подозрительной активности");
        
        // Логируем блокировку
        logAuditEvent('user_blocked', $userId, ['reason' => 'suspicious_activity_threshold_reached']);
    }
}
?>
