<?php
/**
 * api/recordAdView.php
 * API эндпоинт для записи просмотра рекламы и начисления награды (включая реф. бонус).
 */

// Включаем логирование для этого скрипта тоже (если нужно)
// ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }
if (!(@require_once __DIR__ . '/security.php')) { http_response_code(500); error_log('FATAL: security.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: SEC']); exit; }
// --- Конец проверки зависимостей ---

/**
 * Логирование рекламных запросов для статистики
 */
function logAdRequest($userId, $adType, $status, $clientIp, $userAgent = '', $country = '') {
    $logFile = __DIR__ . '/ad_requests.log';
    $timestamp = time();
    $date = date('Y-m-d H:i:s', $timestamp);

    // Определяем страну по IP (простая заглушка, можно интегрировать с GeoIP)
    if (empty($country)) {
        $country = getCountryByIP($clientIp);
    }

    $logEntry = [
        'timestamp' => $timestamp,
        'date' => $date,
        'user_id' => $userId,
        'ad_type' => $adType,
        'status' => $status, // 'request', 'success', 'empty', 'error', 'limit_exceeded'
        'ip' => $clientIp,
        'user_agent' => $userAgent,
        'country' => $country
    ];

    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Определение страны по IP
 * Использует кэширование для оптимизации
 */
function getCountryByIP($ip) {
    // Кэш для IP адресов
    static $ipCache = [];

    if (isset($ipCache[$ip])) {
        return $ipCache[$ip];
    }

    // Проверяем локальные/приватные IP
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        $ipCache[$ip] = 'Local';
        return 'Local';
    }

    // Простая проверка для российских IP диапазонов
    $russianRanges = [
        '/^(185\.250\.|95\.25\.|46\.17\.|78\.85\.|91\.200\.|91\.201\.|91\.202\.|91\.203\.)/',
        '/^(176\.59\.|176\.60\.|176\.61\.|176\.62\.|176\.63\.|176\.64\.|176\.65\.|176\.66\.)/',
        '/^(188\.162\.|188\.163\.|188\.164\.|188\.165\.|188\.166\.|188\.167\.|188\.168\.|188\.169\.)/'
    ];

    foreach ($russianRanges as $range) {
        if (preg_match($range, $ip)) {
            $ipCache[$ip] = 'RU';
            return 'RU';
        }
    }

    // Проверка для американских IP
    if (preg_match('/^(8\.8\.|1\.1\.|208\.67\.|4\.2\.|173\.252\.|31\.13\.)/', $ip)) {
        $ipCache[$ip] = 'US';
        return 'US';
    }

    // Для продакшена можно добавить API запрос (с осторожностью из-за лимитов)
    /*
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 2,
                'user_agent' => 'UniQPaid/1.0'
            ]
        ]);

        $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['countryCode'])) {
                $ipCache[$ip] = $data['countryCode'];
                return $data['countryCode'];
            }
        }
    } catch (Exception $e) {
        error_log("GeoIP API error: " . $e->getMessage());
    }
    */

    $ipCache[$ip] = 'Unknown';
    return 'Unknown';
}

// 1. Получение IP для логирования (без проверки лимитов)
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
// ИСПРАВЛЕНИЕ: Убираем проверку IP rate limiting для кеша
// if (!checkIpRateLimit($clientIp)) {
//     error_log("recordAdView WARNING: IP {$clientIp} заблокирован из-за превышения лимита запросов");
//     http_response_code(429);
//     echo json_encode(['error' => 'Слишком много запросов. Попробуйте позже']);
//     exit;
// }

// 2. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
error_log("recordAdView DEBUG: Получен JSON: " . $inputJSON);

$input = json_decode($inputJSON, true);
error_log("recordAdView DEBUG: Декодированные данные: " . print_r($input, true));

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    error_log("recordAdView ERROR: Нет данных или неверный JSON");

    // Логируем ошибочный запрос
    logAdRequest(0, $input['adType'] ?? 'unknown', 'error', $clientIp, $userAgent);

    http_response_code(400); echo json_encode(['error' => 'Ошибка запроса: Нет данных']); exit;
}
$initData = $input['initData'];
error_log("recordAdView INFO: Получен initData (длина: " . strlen($initData) . ") от IP: {$clientIp}");

// 3. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    // Ошибка уже залогирована внутри validateTelegramInitData

    // Логируем неудачную валидацию
    logAdRequest(0, $input['adType'] ?? 'unknown', 'error', $clientIp, $userAgent);

    http_response_code(403); echo json_encode(['error' => 'Ошибка: Неверные данные']); exit;
}
$userId = intval($validatedData['user']['id']);
error_log("recordAdView INFO: initData валидирован для user $userId от IP: {$clientIp}");

// Логируем запрос рекламы
$adType = $input['adType'] ?? 'default';
logAdRequest($userId, $adType, 'request', $clientIp, $userAgent);

// 4. Загрузка данных ВСЕХ пользователей
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("recordAdView ERROR: loadUserData вернул не массив."); http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: LD1']); exit;
}
error_log("recordAdView INFO: Данные пользователей загружены для обработки награды user $userId.");

// 4. Проверка лимитов и начисление основной награды
$adType = $input['adType'] ?? 'default';
$rewardAmount = 10; // Значение по умолчанию для обратной совместимости

if ($adType === 'native_banner' && defined('AD_REWARD_NATIVE_BANNER')) {
    $rewardAmount = AD_REWARD_NATIVE_BANNER;
} elseif ($adType === 'interstitial' && defined('AD_REWARD_INTERSTITIAL')) {
    $rewardAmount = AD_REWARD_INTERSTITIAL;
} elseif ($adType === 'rewarded_video' && defined('AD_REWARD_REWARDED_VIDEO')) {
    $rewardAmount = AD_REWARD_REWARDED_VIDEO;
}

// Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("recordAdView WARNING: Попытка начисления монет заблокированному пользователю $userId");

    // Логируем попытку заблокированного пользователя
    logAdRequest($userId, $adType, 'blocked_user', $clientIp, $userAgent);

    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// Проверяем лимит просмотров рекламы по типу
if (!checkAdViewLimitByType($userId, $adType, $userData)) {
    error_log("recordAdView WARNING: Превышен лимит просмотров рекламы типа $adType для пользователя $userId");

    // Логируем превышение лимита
    logAdRequest($userId, $adType, 'limit_exceeded', $clientIp, $userAgent);

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, "ad_view_limit_exceeded_type_{$adType}");

    http_response_code(429);
    echo json_encode(['error' => "Превышен лимит просмотров рекламы типа '$adType'. Попробуйте позже"]);
    exit;
}

// Также проверяем общий лимит для дополнительной защиты
if (!checkAdViewLimit($userId, $userData)) {
    error_log("recordAdView WARNING: Превышен общий лимит просмотров рекламы для пользователя $userId");

    // Логируем превышение общего лимита
    logAdRequest($userId, $adType, 'general_limit_exceeded', $clientIp, $userAgent);

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'ad_view_general_limit_exceeded');

    http_response_code(429);
    echo json_encode(['error' => 'Превышен общий лимит просмотров рекламы. Попробуйте позже']);
    exit;
}

// Логируем событие просмотра рекламы
logAuditEvent('ad_view', $userId, ['reward' => $rewardAmount, 'type' => $input['adType'] ?? 'unknown']);

// Начисляем награду
$newBalanceUser = increaseUserBalance($userId, $rewardAmount, $userData);
if ($newBalanceUser === false) {
    // Ошибка залогирована в increaseUserBalance

    // Логируем ошибку начисления
    logAdRequest($userId, $adType, 'error', $clientIp, $userAgent);

    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: Не удалось обновить баланс']);
    exit;
}

// Обновляем общую сумму заработанных монет
if (!isset($userData[$userId]['total_earned'])) {
    $userData[$userId]['total_earned'] = $rewardAmount;
} else {
    $userData[$userId]['total_earned'] += $rewardAmount;
}

error_log("recordAdView INFO: Основная награда $rewardAmount начислена user $userId. Новый баланс: $newBalanceUser. Всего заработано: {$userData[$userId]['total_earned']}");

// 5. Начисление реферального бонуса
$referrerId = getUserReferrerId($userId, $userData);
if ($referrerId !== null) {
    error_log("recordAdView INFO: Найден реферер $referrerId для user $userId.");
    // Проверяем, существует ли реферер в данных (мог быть удален)
    if (isset($userData[$referrerId])) {
        $bonusPercent = defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT : 0.10;
        $bonusAmount = floor($rewardAmount * $bonusPercent); // Округляем вниз

        if ($bonusAmount > 0) {
            $newBalanceReferrer = increaseUserBalance($referrerId, $bonusAmount, $userData);
            if ($newBalanceReferrer !== false) {
                // Обновляем статистику реферальных начислений (ТОЛЬКО бонусы от рефералов)
                if (!isset($userData[$referrerId]['referral_earnings'])) {
                    $userData[$referrerId]['referral_earnings'] = 0;
                }
                $userData[$referrerId]['referral_earnings'] += $bonusAmount;

                error_log("recordAdView INFO: Успешно начислен реф. бонус $bonusAmount пользователю $referrerId. Новый баланс реферера: $newBalanceReferrer, Всего заработано на рефералах: " . $userData[$referrerId]['referral_earnings']);
            } else {
                // Ошибка уже залогирована в increaseUserBalance
                error_log("recordAdView ERROR: Не удалось начислить реф. бонус $bonusAmount пользователю $referrerId (возможно, из-за ошибки в increaseUserBalance).");
                // Не прерываем выполнение, т.к. основная награда начислена
            }
        } else {
            error_log("recordAdView INFO: Реф. бонус для $referrerId равен 0 (награда: $rewardAmount, процент: $bonusPercent).");
        }
    } else {
         error_log("recordAdView WARNING: Реферер $referrerId для пользователя $userId не найден в актуальных данных (возможно, был удален).");
    }
}

// 6. Сохранение ВСЕХ измененных данных
if (!saveUserData($userData)) {
    error_log("recordAdView CRITICAL ERROR: Не удалось сохранить данные после начислений для $userId (и возможно реферера $referrerId)");
    http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: Не удалось сохранить данные']); exit;
}
error_log("recordAdView INFO: Все данные успешно сохранены после обработки награды для $userId.");

// 7. Логируем успешный показ рекламы
logAdRequest($userId, $adType, 'success', $clientIp, $userAgent);

// 8. Успешный ответ с полными данными для ЕДИНОЙ СИСТЕМЫ
http_response_code(200); // OK
echo json_encode([
    'success' => true,
    'newBalance' => $newBalanceUser,
    'reward' => $rewardAmount,
    'adType' => $adType,
    'timestamp' => time(),
    'message' => "Награда успешно начислена"
]);
error_log("recordAdView INFO: Успешный ответ отправлен для user $userId с наградой $rewardAmount.");
exit;
?>